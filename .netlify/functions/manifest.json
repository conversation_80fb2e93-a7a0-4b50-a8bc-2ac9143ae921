{"functions": [{"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/personal_github_projects/swe-ai-manifesto/netlify/functions/signatures.js", "name": "signatures", "priority": 10, "runtimeVersion": "nodejs18.x", "path": "/Users/<USER>/personal_github_projects/swe-ai-manifesto/.netlify/functions/signatures.zip", "runtime": "js"}], "system": {"arch": "arm64", "platform": "darwin"}, "timestamp": 1759149108406, "version": 1}