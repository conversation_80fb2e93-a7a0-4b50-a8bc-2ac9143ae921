"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveModuleExports = void 0;
let mockResults = {};
const resolveModuleExports = (input) => {
    // return a mocked result
    if (typeof input === `string`) {
        return mockResults[input];
    }
    // return default result
    if (typeof input !== `object`) {
        return [];
    }
    // set mock results
    mockResults = Object.assign({}, input);
    return undefined;
};
exports.resolveModuleExports = resolveModuleExports;
//# sourceMappingURL=resolve-module-exports.js.map