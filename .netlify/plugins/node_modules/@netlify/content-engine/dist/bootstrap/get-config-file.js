"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConfigFile = getConfigFile;
const dynamic_import_1 = require("../../lib/dynamic-import");
const fs_extra_1 = __importDefault(require("fs-extra"));
const test_import_error_1 = require("../utils/test-import-error");
const reporter_1 = __importDefault(require("../reporter"));
const path_1 = __importDefault(require("path"));
const is_near_match_1 = require("../utils/is-near-match");
const resolve_js_file_path_1 = require("./resolve-js-file-path");
const prefer_default_1 = require("./prefer-default");
async function getConfigFile(siteDirectory, configName, distance = 3) {
    const uncompiledResult = await attemptImportUncompiled(siteDirectory, configName, distance);
    return uncompiledResult || {};
}
async function attemptImport(siteDirectory, configPath) {
    const configFilePath = await (0, resolve_js_file_path_1.resolveJSFilepath)({
        rootDir: siteDirectory,
        filePath: configPath,
    });
    // The file does not exist, no sense trying to import it
    if (!configFilePath) {
        return { configFilePath: ``, configModule: undefined };
    }
    const filePath = (0, resolve_js_file_path_1.maybeAddFileProtocol)(configFilePath);
    const importedModule = await (0, dynamic_import_1.dynamicImport)(filePath);
    const configModule = (0, prefer_default_1.preferDefault)(importedModule);
    return { configFilePath, configModule };
}
async function attemptImportUncompiled(siteDirectory, configName, distance) {
    let uncompiledResult;
    const uncompiledConfigPath = path_1.default.join(siteDirectory, configName);
    try {
        uncompiledResult = await attemptImport(siteDirectory, uncompiledConfigPath);
    }
    catch (error) {
        if (!(0, test_import_error_1.testImportError)(uncompiledConfigPath, error)) {
            reporter_1.default.panic({
                id: `10123`,
                error,
                context: {
                    configName,
                    message: error.message,
                },
            });
        }
    }
    if (uncompiledResult?.configFilePath) {
        return uncompiledResult;
    }
    const error = new Error(`Cannot find package '${uncompiledConfigPath}'`);
    const { tsConfig, nearMatch } = await checkTsAndNearMatch(siteDirectory, configName, distance);
    // gatsby-config.ts exists but compiled gatsby-config.js does not
    if (tsConfig) {
        reporter_1.default.panic({
            id: `10127`,
            error,
            context: {
                configName,
            },
        });
    }
    // gatsby-config is misnamed
    if (nearMatch) {
        const isTSX = nearMatch.endsWith(`.tsx`);
        reporter_1.default.panic({
            id: `10124`,
            error,
            context: {
                configName,
                nearMatch,
                isTSX,
            },
        });
    }
    // gatsby-config is incorrectly located in src directory
    const isInSrcDir = await (0, resolve_js_file_path_1.resolveJSFilepath)({
        rootDir: siteDirectory,
        filePath: path_1.default.join(siteDirectory, `src`, configName),
        warn: false,
    });
    if (isInSrcDir) {
        reporter_1.default.panic({
            id: `10125`,
            context: {
                configName,
            },
        });
    }
    return uncompiledResult;
}
async function checkTsAndNearMatch(siteDirectory, configName, distance) {
    const files = await fs_extra_1.default.readdir(siteDirectory);
    let tsConfig = false;
    let nearMatch = ``;
    for (const file of files) {
        if (tsConfig || nearMatch) {
            break;
        }
        const { name, ext } = path_1.default.parse(file);
        if (name === configName && ext === `.ts`) {
            tsConfig = true;
            break;
        }
        if ((0, is_near_match_1.isNearMatch)(name, configName, distance)) {
            nearMatch = file;
        }
    }
    return {
        tsConfig,
        nearMatch,
    };
}
//# sourceMappingURL=get-config-file.js.map