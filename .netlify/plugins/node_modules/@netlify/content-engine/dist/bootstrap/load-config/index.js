"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadConfig = loadConfig;
const reporter_1 = __importDefault(require("../../reporter"));
// import telemetry from "gatsby-telemetry"
const prefer_default_1 = require("../prefer-default");
const get_config_file_1 = require("../get-config-file");
const actions_1 = require("../../redux/actions");
const load_themes_1 = require("../load-themes");
const redux_1 = require("../../redux");
async function loadConfig({ siteDirectory, providedConfig, }) {
    let config;
    let configFilePath;
    if (providedConfig) {
        config = providedConfig;
    }
    else {
        // Try opening the site's gatsby-config.js file.
        const configFile = await (0, get_config_file_1.getConfigFile)(siteDirectory, `gatsby-config`);
        configFilePath = configFile.configFilePath;
        config = (0, prefer_default_1.preferDefault)(configFile.configModule);
    }
    // The root config cannot be exported as a function, only theme configs
    if (typeof config === `function`) {
        reporter_1.default.panic({
            id: `10126`,
            context: {
                configName: `gatsby-config`,
                siteDirectory,
            },
        });
    }
    // theme gatsby configs can be functions or objects
    if (config &&
        // themes currently don't work when a config object is provided
        !providedConfig) {
        const plugins = await (0, load_themes_1.loadThemes)(config, {
            configFilePath,
            rootDir: siteDirectory,
        });
        config = plugins.config;
    }
    redux_1.store.dispatch(actions_1.internalActions.setSiteConfig(config));
    return config;
}
//# sourceMappingURL=index.js.map