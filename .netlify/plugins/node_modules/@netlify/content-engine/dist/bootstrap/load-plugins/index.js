"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadPlugins = loadPlugins;
const redux_1 = require("../../redux");
const nodeAPIs = __importStar(require("../../utils/api-node-docs"));
const load_internal_plugins_1 = require("./load-internal-plugins");
const validate_1 = require("./validate");
const normalize_1 = require("./utils/normalize");
const get_api_1 = require("./utils/get-api");
const flatten_plugins_1 = require("./utils/flatten-plugins");
async function loadPlugins(rawConfig, rootDir, additionalPluginDirectories) {
    // Turn all strings in plugins: [`...`] into the { resolve: ``, options: {} } form
    const config = (0, normalize_1.normalizeConfig)(rawConfig);
    const currentAPIs = (0, get_api_1.getAPI)({
        node: nodeAPIs,
    });
    // Collate internal plugins, site config plugins, site default plugins
    const pluginInfos = (0, load_internal_plugins_1.loadInternalPlugins)(config, rootDir, additionalPluginDirectories);
    // Create a flattened array of the plugins
    const pluginArray = (0, flatten_plugins_1.flattenPlugins)(pluginInfos);
    // Work out which plugins use which APIs, including those which are not
    // valid Gatsby APIs, aka 'badExports'
    const { flattenedPlugins, badExports } = await (0, validate_1.collatePluginAPIs)({
        currentAPIs,
        flattenedPlugins: pluginArray,
        rootDir,
    });
    // Show errors for any non-Gatsby APIs exported from plugins
    await (0, validate_1.handleBadExports)({ currentAPIs, badExports });
    // If we get this far, everything looks good. Update the store
    redux_1.store.dispatch({
        type: `SET_SITE_FLATTENED_PLUGINS`,
        payload: flattenedPlugins,
    });
    return flattenedPlugins;
}
//# sourceMappingURL=index.js.map