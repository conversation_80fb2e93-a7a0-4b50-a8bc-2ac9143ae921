"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadInternalPlugins = loadInternalPlugins;
const path_1 = require("../../core-utils/path");
const lodash_uniqwith_1 = __importDefault(require("lodash.uniqwith"));
const lodash_isequal_1 = __importDefault(require("lodash.isequal"));
const path_2 = __importDefault(require("path"));
const process_plugin_1 = require("./process-plugin");
const create_id_1 = require("./utils/create-id");
const create_hash_1 = require("./utils/create-hash");
function loadInternalPlugins(config = {}, rootDir, additionalPluginDirectories) {
    // Instantiate plugins.
    const plugins = [];
    // Add internal plugins
    const internalPlugins = [
        `../../internal-plugins/internal-data-bridge`,
    ].filter(Boolean);
    internalPlugins.forEach((relPath) => {
        const absPath = path_2.default.join(__dirname, relPath);
        plugins.push((0, process_plugin_1.processPlugin)(absPath, rootDir));
    });
    // Add plugins from the site config.
    if (config.plugins) {
        config.plugins.forEach((plugin) => {
            if (
            // don't process default-site-plugin
            plugin.resolve === `default-site-plugin` ||
                // @ts-ignore
                plugin === `default-site-plugin`) {
                return;
            }
            const processedPlugin = (0, process_plugin_1.processPlugin)(plugin, rootDir, additionalPluginDirectories);
            plugins.push(processedPlugin);
        });
    }
    // Add the site's default "plugin" i.e. engine-node.js file in root of site.
    plugins.push({
        resolve: (0, path_1.slash)(process.cwd()),
        id: (0, create_id_1.createPluginId)(`default-site-plugin`),
        name: `default-site-plugin`,
        version: (0, create_hash_1.createFileContentHash)(process.cwd(), `gatsby-*`) +
            (0, create_hash_1.createFileContentHash)(process.cwd(), `engine-*`),
        pluginOptions: {
            plugins: [],
        },
    });
    const uniquePlugins = (0, lodash_uniqwith_1.default)(plugins, lodash_isequal_1.default);
    return uniquePlugins;
}
//# sourceMappingURL=load-internal-plugins.js.map