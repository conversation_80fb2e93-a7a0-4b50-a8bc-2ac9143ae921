{"version": 3, "file": "load-internal-plugins.js", "sourceRoot": "", "sources": ["../../../src/bootstrap/load-plugins/load-internal-plugins.ts"], "names": [], "mappings": ";;;;;AASA,kDAuDC;AAhED,gDAA8C;AAC9C,sEAAuC;AACvC,oEAAqC;AACrC,gDAAwB;AAExB,qDAAiD;AACjD,iDAAmD;AACnD,qDAA4D;AAE5D,SAAgB,mBAAmB,CACjC,SAAsB,EAAE,EACxB,OAAe,EACf,2BAAsC;IAEtC,uBAAuB;IACvB,MAAM,OAAO,GAAuB,EAAE,CAAC;IAEvC,uBAAuB;IACvB,MAAM,eAAe,GAAG;QACtB,6CAA6C;KAC9C,CAAC,MAAM,CAAC,OAAO,CAAkB,CAAC;IAEnC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAClC,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC9C,OAAO,CAAC,IAAI,CAAC,IAAA,8BAAa,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChC;YACE,oCAAoC;YACpC,MAAM,CAAC,OAAO,KAAK,qBAAqB;gBACxC,aAAa;gBACb,MAAM,KAAK,qBAAqB,EAChC,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,eAAe,GAAG,IAAA,8BAAa,EACnC,MAAM,EACN,OAAO,EACP,2BAA2B,CAC5B,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,OAAO,CAAC,IAAI,CAAC;QACX,OAAO,EAAE,IAAA,YAAK,EAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QAC7B,EAAE,EAAE,IAAA,0BAAc,EAAC,qBAAqB,CAAC;QACzC,IAAI,EAAE,qBAAqB;QAC3B,OAAO,EACL,IAAA,mCAAqB,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC;YAChD,IAAA,mCAAqB,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC;QAClD,aAAa,EAAE;YACb,OAAO,EAAE,EAAE;SACZ;KACF,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,IAAA,yBAAQ,EAAC,OAAO,EAAE,wBAAO,CAAC,CAAC;IAEjD,OAAO,aAAa,CAAC;AACvB,CAAC"}