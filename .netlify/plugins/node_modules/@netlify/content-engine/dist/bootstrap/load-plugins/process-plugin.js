"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processPlugin = processPlugin;
const create_id_1 = require("./utils/create-id");
const resolve_plugin_1 = require("./resolve-plugin");
const lodash_merge_1 = __importDefault(require("lodash.merge"));
const lodash_isempty_1 = __importDefault(require("lodash.isempty"));
function processPlugin(plugin, rootDir, additionalPluginDirectories = []) {
    // Respect the directory that the plugin was sourced from initially
    rootDir = (typeof plugin !== `string` && plugin.parentDir) || rootDir;
    if (typeof plugin === `string`) {
        const info = (0, resolve_plugin_1.resolvePluginFromListOfDirectories)(plugin, [
            rootDir,
            ...additionalPluginDirectories,
        ]);
        return {
            ...info,
            pluginOptions: {
                plugins: [],
            },
        };
    }
    plugin.options = plugin.options || {};
    // Throw an error if there is an "option" key.
    if ((0, lodash_isempty_1.default)(plugin.options) &&
        !(0, lodash_isempty_1.default)(plugin.option)) {
        throw new Error(`Plugin "${plugin.resolve}" has an "option" key in the configuration. Did you mean "options"?`);
    }
    // Add some default values for tests as we don't actually
    // want to try to load anything during tests.
    if (plugin.resolve === `___TEST___`) {
        const name = `TEST`;
        return {
            id: (0, create_id_1.createPluginId)(name, plugin),
            name,
            version: `0.0.0-test`,
            pluginOptions: {
                plugins: [],
            },
            resolve: `__TEST__`,
        };
    }
    const info = (0, resolve_plugin_1.resolvePluginFromListOfDirectories)(plugin, [
        rootDir,
        ...additionalPluginDirectories,
    ]);
    return {
        ...info,
        modulePath: plugin.modulePath,
        module: plugin.module,
        subPluginPaths: plugin.subPluginPaths
            ? Array.from(plugin.subPluginPaths)
            : undefined,
        id: (0, create_id_1.createPluginId)(info.name, plugin),
        pluginOptions: (0, lodash_merge_1.default)({ plugins: [] }, plugin.options),
    };
}
//# sourceMappingURL=process-plugin.js.map