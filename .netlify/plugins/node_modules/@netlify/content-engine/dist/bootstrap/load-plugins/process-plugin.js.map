{"version": 3, "file": "process-plugin.js", "sourceRoot": "", "sources": ["../../../src/bootstrap/load-plugins/process-plugin.ts"], "names": [], "mappings": ";;;;;AAMA,sCAiEC;AAtED,iDAAmD;AACnD,qDAAsE;AACtE,gEAAiC;AACjC,oEAAqC;AAErC,SAAgB,aAAa,CAC3B,MAAiB,EACjB,OAAe,EACf,8BAAwC,EAAE;IAE1C,mEAAmE;IACnE,OAAO,GAAG,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC;IAEtE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,MAAM,IAAI,GAAG,IAAA,mDAAkC,EAAC,MAAM,EAAE;YACtD,OAAO;YACP,GAAG,2BAA2B;SAC/B,CAAC,CAAC;QAEH,OAAO;YACL,GAAG,IAAI;YACP,aAAa,EAAE;gBACb,OAAO,EAAE,EAAE;aACZ;SACF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;IAEtC,8CAA8C;IAC9C,IACE,IAAA,wBAAO,EAAC,MAAM,CAAC,OAAO,CAAC;QACvB,CAAC,IAAA,wBAAO,EAAE,MAA+B,CAAC,MAAM,CAAC,EACjD,CAAC;QACD,MAAM,IAAI,KAAK,CACb,WAAW,MAAM,CAAC,OAAO,qEAAqE,CAC/F,CAAC;IACJ,CAAC;IAED,yDAAyD;IACzD,6CAA6C;IAC7C,IAAI,MAAM,CAAC,OAAO,KAAK,YAAY,EAAE,CAAC;QACpC,MAAM,IAAI,GAAG,MAAM,CAAC;QAEpB,OAAO;YACL,EAAE,EAAE,IAAA,0BAAc,EAAC,IAAI,EAAE,MAAM,CAAC;YAChC,IAAI;YACJ,OAAO,EAAE,YAAY;YACrB,aAAa,EAAE;gBACb,OAAO,EAAE,EAAE;aACZ;YACD,OAAO,EAAE,UAAU;SACpB,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,GAAG,IAAA,mDAAkC,EAAC,MAAM,EAAE;QACtD,OAAO;QACP,GAAG,2BAA2B;KAC/B,CAAC,CAAC;IAEH,OAAO;QACL,GAAG,IAAI;QACP,UAAU,EAAE,MAAM,CAAC,UAAU;QAC7B,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,cAAc,EAAE,MAAM,CAAC,cAAc;YACnC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACnC,CAAC,CAAC,SAAS;QACb,EAAE,EAAE,IAAA,0BAAc,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;QACrC,aAAa,EAAE,IAAA,sBAAK,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC;KACtD,CAAC;AACJ,CAAC"}