import { IPluginInfo, PluginRef } from "./types";
export declare function resolvePluginFromListOfDirectories(plugin: PluginRef, rootDirs: string[]): IPluginInfo;
/**
 * @param plugin
 * This should be a plugin spec object where possible but can also be the
 * name of a plugin.
 *
 * When it is a name, it can be a name of a local plugin, the name of a plugin
 * located in node_modules, or a Gatsby internal plugin. In the last case the
 * plugin will be an absolute path.
 * @param rootDir
 * This is the project location, from which are found the plugins
 */
export declare function resolvePlugin(plugin: PluginRef, rootDir: string): IPluginInfo;
//# sourceMappingURL=resolve-plugin.d.ts.map