"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolvePluginFromListOfDirectories = resolvePluginFromListOfDirectories;
exports.resolvePlugin = resolvePlugin;
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const path_2 = require("../../core-utils/path");
const create_require_from_path_1 = require("../../core-utils/create-require-from-path");
const validate_1 = require("./validate");
const create_id_1 = require("./utils/create-id");
const create_hash_1 = require("./utils/create-hash");
const check_local_plugin_1 = require("./utils/check-local-plugin");
function resolvePluginFromListOfDirectories(plugin, rootDirs) {
    const errorMessages = [];
    for (const rootDir of rootDirs) {
        try {
            return resolvePlugin(plugin, rootDir);
        }
        catch (e) {
            if (e instanceof Error) {
                errorMessages.push(e.message);
            }
            else {
                throw e;
            }
        }
    }
    throw new Error(errorMessages.join(`\n`));
}
/**
 * @param plugin
 * This should be a plugin spec object where possible but can also be the
 * name of a plugin.
 *
 * When it is a name, it can be a name of a local plugin, the name of a plugin
 * located in node_modules, or a Gatsby internal plugin. In the last case the
 * plugin will be an absolute path.
 * @param rootDir
 * This is the project location, from which are found the plugins
 */
function resolvePlugin(plugin, rootDir) {
    const pluginName = typeof plugin === `string` ? plugin : plugin.resolve;
    // Handle local plugins
    const { validLocalPlugin, localPluginPath = `` } = (0, check_local_plugin_1.checkLocalPlugin)(plugin, rootDir);
    if (validLocalPlugin && localPluginPath) {
        const packageJSON = JSON.parse(fs_1.default.readFileSync(`${localPluginPath}/package.json`, `utf-8`));
        const name = packageJSON.name || pluginName;
        (0, validate_1.warnOnIncompatiblePeerDependency)(name, packageJSON);
        return {
            resolve: localPluginPath,
            name,
            id: (0, create_id_1.createPluginId)(name),
            version: packageJSON?.version || (0, create_hash_1.createFileContentHash)(localPluginPath, `**`),
        };
    }
    /**
     * Here we have an absolute path to an internal plugin, or a name of a module
     * which should be located in node_modules.
     */
    try {
        const requireSource = rootDir !== null
            ? (0, create_require_from_path_1.createRequireFromPath)(`${rootDir}/:internal:`)
            : require;
        // If the path is absolute, resolve the directory of the internal plugin,
        // otherwise resolve the directory containing the package.json
        const resolvedPath = (0, path_2.slash)(path_1.default.dirname(requireSource.resolve(path_1.default.isAbsolute(pluginName)
            ? pluginName
            : `${pluginName}/package.json`)));
        const packageJSON = JSON.parse(fs_1.default.readFileSync(`${resolvedPath}/package.json`, `utf-8`));
        (0, validate_1.warnOnIncompatiblePeerDependency)(packageJSON.name, packageJSON);
        return {
            resolve: resolvedPath,
            id: (0, create_id_1.createPluginId)(packageJSON.name),
            name: packageJSON.name,
            version: packageJSON.version,
        };
    }
    catch (err) {
        throw new Error(`Could not load plugin ${pluginName} in ${rootDir}. Is it installed?\nError: ${err.stack}`);
    }
}
//# sourceMappingURL=resolve-plugin.js.map