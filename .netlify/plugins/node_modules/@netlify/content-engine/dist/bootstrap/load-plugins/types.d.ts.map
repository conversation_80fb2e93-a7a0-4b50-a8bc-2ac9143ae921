{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/bootstrap/load-plugins/types.ts"], "names": [], "mappings": "AAAA,MAAM,WAAW,cAAc;IAC7B,OAAO,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;CAC5B;AAED,MAAM,WAAW,WAAY,SAAQ,cAAc;IACjD,OAAO,CAAC,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC;CACnC;AAMD,MAAM,WAAW,WAAW;IAC1B,oCAAoC;IACpC,EAAE,EAAE,MAAM,CAAC;IAEX,sCAAsC;IACtC,OAAO,EAAE,MAAM,CAAC;IAEhB,qFAAqF;IACrF,0BAA0B,CAAC,EAAE,MAAM,CAAC;IAEpC,sBAAsB;IACtB,IAAI,EAAE,MAAM,CAAC;IAEb,+CAA+C;IAC/C,OAAO,EAAE,MAAM,CAAC;IAEhB,mCAAmC;IACnC,aAAa,CAAC,EAAE,kBAAkB,CAAC;IAEnC,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC/B,MAAM,CAAC,EAAE,GAAG,CAAC;IACb,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,kBAAkB;IACjC,OAAO,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;IAC7B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC;CAC3B;AAED,MAAM,WAAW,gBAAiB,SAAQ,WAAW;IACnD,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CACzB;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,iBAAiB,CAAC;IAC5B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC/B,MAAM,CAAC,EAAE,GAAG,CAAC;IACb,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,gBAAgB,CAAC;AAElD,MAAM,WAAW,iBAAiB;IAChC,OAAO,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;IAC3B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC;CAC3B"}