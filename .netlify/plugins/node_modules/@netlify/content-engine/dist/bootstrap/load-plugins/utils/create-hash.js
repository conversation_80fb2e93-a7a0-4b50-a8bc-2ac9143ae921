"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFileContentHash = createFileContentHash;
const fs_1 = __importDefault(require("fs"));
const crypto_1 = __importDefault(require("crypto"));
const glob_1 = require("glob");
function createFileContentHash(root, globPattern) {
    // TODO: Use hash-wasm
    const hash = crypto_1.default.createHash(`md5`);
    const files = glob_1.glob.sync(`${root}/${globPattern}`, { nodir: true });
    files.forEach((filepath) => {
        hash.update(fs_1.default.readFileSync(filepath));
    });
    return hash.digest(`hex`);
}
//# sourceMappingURL=create-hash.js.map