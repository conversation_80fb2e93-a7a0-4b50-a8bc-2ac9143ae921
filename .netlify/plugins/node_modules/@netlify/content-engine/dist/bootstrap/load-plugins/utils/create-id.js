"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPluginId = void 0;
const create_node_id_1 = require("../../../utils/create-node-id");
/**
 * Make sure key is unique to plugin options. E.g. there could
 * be multiple source-filesystem plugins, with different names
 * (docs, blogs).
 *
 * @param name Name of the plugin
 * @param pluginObject Object of the plugin
 */
const createPluginId = (name, pluginObject = null) => (0, create_node_id_1.createNodeId)(name + (pluginObject ? JSON.stringify(pluginObject.options) : ``), `Plugin`);
exports.createPluginId = createPluginId;
//# sourceMappingURL=create-id.js.map