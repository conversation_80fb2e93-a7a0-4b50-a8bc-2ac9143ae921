{"version": 3, "file": "flatten-plugins.js", "sourceRoot": "", "sources": ["../../../../src/bootstrap/load-plugins/utils/flatten-plugins.ts"], "names": [], "mappings": ";;;AAEA,4DAA4D;AAC5D,mEAAmE;AACnE,kBAAkB;AACX,MAAM,cAAc,GAAG,CAC5B,OAA2B,EACP,EAAE;IACtB,MAAM,SAAS,GAAuB,EAAE,CAAC;IACzC,MAAM,cAAc,GAAG,CAAC,MAAmB,EAAQ,EAAE;QACnD,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAClD,SAAS;gBACT,6EAA6E;gBAC7E,uGAAuG;gBACvG,6FAA6F;gBAC7F,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1C,IAAI,KAAK,GAAe,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBAC/C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;wBACrB,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;oBACvB,CAAC;yBAAM,CAAC;wBACN,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC7C,CAAC;gBACH,CAAC;gBACD,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;gBAErB,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;oBAC1B,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC1B,cAAc,CAAC,SAAS,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACzB,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,cAAc,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AApCW,QAAA,cAAc,kBAoCzB"}