import { ISiteConfig, IRawSiteConfig } from "../types";
import { IPluginRefObject, PluginRef } from "../../../plugin-utils/types";
export declare function normalizePlugin(plugin: IPluginRefObject | string): IPluginRefObject;
export declare function normalizePlugins(plugins?: Array<PluginRef>): Array<IPluginRefObject>;
export declare const normalizeConfig: (config?: IRawSiteConfig) => ISiteConfig;
//# sourceMappingURL=normalize.d.ts.map