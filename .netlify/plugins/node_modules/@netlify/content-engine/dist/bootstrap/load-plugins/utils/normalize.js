"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalizeConfig = void 0;
exports.normalizePlugin = normalizePlugin;
exports.normalizePlugins = normalizePlugins;
function normalizePlugin(plugin) {
    if (typeof plugin === `string`) {
        return {
            resolve: plugin,
            options: {},
        };
    }
    if (plugin.options?.plugins) {
        plugin.options = {
            ...plugin.options,
            plugins: normalizePlugins(plugin.options.plugins),
        };
    }
    return plugin;
}
function normalizePlugins(plugins) {
    return (plugins || []).map(normalizePlugin);
}
const normalizeConfig = (config = {}) => {
    return {
        ...config,
        plugins: (config.plugins || []).map(normalizePlugin),
    };
};
exports.normalizeConfig = normalizeConfig;
//# sourceMappingURL=normalize.js.map