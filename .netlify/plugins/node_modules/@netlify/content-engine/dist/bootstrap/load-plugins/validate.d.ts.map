{"version": 3, "file": "validate.d.ts", "sourceRoot": "", "sources": ["../../../src/bootstrap/load-plugins/validate.ts"], "names": [], "mappings": "AAaA,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAC1C,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAC;AAMxD,UAAU,IAAI;IACZ,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,MAAM;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,CAAC;IACtB,GAAG,CAAC,EAAE,IAAI,CAAC;CACZ;AAED,MAAM,MAAM,UAAU,GAAG,MAAM,CAAC;AAEhC,KAAK,SAAS,GAAG;KACd,UAAU,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;CAC1C,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;KACxB,UAAU,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;CAC1C,CAAC;AA2GF,wBAAsB,gBAAgB,CAAC,EACrC,WAAW,EACX,UAAU,GACX,EAAE;IACD,WAAW,EAAE,YAAY,CAAC;IAC1B,UAAU,EAAE;SAAG,GAAG,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;KAAE,CAAC;CACpD,GAAG,OAAO,CAAC,IAAI,CAAC,CAuBhB;AAED;;GAEG;AACH,wBAAsB,iBAAiB,CAAC,EACtC,WAAW,EACX,gBAAgB,EAChB,OAAO,GACR,EAAE;IACD,WAAW,EAAE,YAAY,CAAC;IAC1B,gBAAgB,EAAE,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACjE,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,GAAG,OAAO,CAAC;IACV,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC1C,UAAU,EAAE,SAAS,CAAC;CACvB,CAAC,CA+BD;AAED,wBAAgB,gCAAgC,CAC9C,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,WAAW,GACvB,IAAI,CAgBN"}