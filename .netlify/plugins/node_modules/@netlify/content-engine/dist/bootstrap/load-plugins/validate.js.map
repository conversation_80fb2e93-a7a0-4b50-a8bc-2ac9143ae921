{"version": 3, "file": "validate.js", "sourceRoot": "", "sources": ["../../../src/bootstrap/load-plugins/validate.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkJA,4CA6BC;AAKD,8CA0CC;AAED,4EAmBC;AAnPD,sDAAyB;AACzB,oEAAqC;AACrC,0EAA2C;AAC3C,8EAA+C;AAC/C,4DAA6B;AAC7B,gDAAwB;AACxB,+CAAiC;AACjC,oEAAsD;AACtD,8DAAsC;AACtC,sDAA6B;AAC7B,8CAA8C;AAC9C,sEAAiE;AACjE,iEAA4D;AAI5D,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,KAAK,CAC3C,iBAAE,CAAC,YAAY,CAAC,cAAI,CAAC,IAAI,CAAC,iBAAM,CAAC,IAAI,CAAC,SAAS,CAAE,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAC5E,CAAC;AAuBF,MAAM,uBAAuB,GAAG,CAAC,OAA8B,EAAU,EAAE,CACzE,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;IAChC,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC;YACrD,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;YACnB,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,EAAE,EAAE,CAAC,CAAC;AAET,qEAAqE;AACrE,oEAAoE;AACpE,SAAS,aAAa,CACpB,MAAmB,EACnB,aAAoC,EACpC,IAA2B;IAE3B,IAAI,UAAU,GAAkB,EAAE,CAAC;IACnC,0DAA0D;IAC1D,UAAU,GAAG,UAAU,CAAC,MAAM,CAC5B,IAAA,2BAAU,EAAC,aAAa,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACxC,OAAO;YACL,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,MAAM,CAAC,IAAI;YACvB,aAAa,EAAE,MAAM,CAAC,OAAO;SAC9B,CAAC;IACJ,CAAC,CAAC,CACH,CAAC;IACF,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,eAAe,CACtB,UAAyB,EACzB,UAAsB,EACtB,WAAyB,EACzB,UAA0E;IAQ1E,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;QACpC,OAAO;YACL,GAAG,EAAE;YACL,GAAG,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC;SAC3C,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAkB,EAAE,CAAC;IACjC,MAAM,KAAK,GAAG,oBAAoB;QAChC,CAAC,CAAC,CAAC,uBAAuB,oBAAoB,EAAE,CAAC;QACjD,CAAC,CAAC,EAAE,CAAC;IAEP,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACxB,MAAM,YAAY,GAAG,gBAAgB,CAAC,aAAa,CACjD,KAAK,CAAC,UAAU,EAChB,WAAW,CAAC,UAAU,CAAC,CACxB,CAAC;QACF,MAAM,eAAe,GAAG,KAAK,CAAC,UAAU,IAAI,qBAAqB,CAAC;QAElE,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG;YACvB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;gBACjB,CAAC,CAAC,4BAA4B,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;gBACjD,CAAC,CAAC,4CAA4C;YAChD,CAAC,CAAC,oBAAoB,CAAC;QAEzB,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CACT,uBAAuB,UAAU,yBAAyB,KAAK,CAAC,UAAU,WAAW,OAAO,GAAG,CAChG,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CACT,gBAAgB,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,aAAa,sBAAsB,KAAK,CAAC,UAAU,WAAW,OAAO,GAAG,CACnH,CAAC;QACJ,CAAC;QAED,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACxC,KAAK,CAAC,IAAI,CACR,WAAW,KAAK,CAAC,UAAU,SAAS,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,CACrE,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,aAAa,EAAE;YACb,gEAAgE;SACjE;aACE,MAAM,CAAC,MAAM,CAAC;aACd,MAAM,CACL,KAAK,CAAC,MAAM,GAAG,CAAC;YACd,CAAC,CAAC,CAAC,IAAI,EAAE,kDAAkD,EAAE,GAAG,KAAK,CAAC;YACtE,CAAC,CAAC,EAAE,CACP;aACA,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,IAAI,CAAC;KACd,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,gBAAgB,CAAC,EACrC,WAAW,EACX,UAAU,GAIX;IACC,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAChD,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CACpC,CAAC;IACF,IAAI,aAAa,EAAE,CAAC;QAClB,MAAM,UAAU,GAAG,MAAM,IAAA,+BAAa,GAAE,CAAC;QACzC,4CAA4C;QAC5C,IAAA,wBAAO,EAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACtC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;YACtC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,eAAe,CAC7B,OAAO,EACP,UAAqC,EACrC,WAAW,EACX,UAAU,CACX,CAAC;gBACF,kBAAQ,CAAC,KAAK,CAAC;oBACb,EAAE,EAAE,OAAO;oBACX,OAAO;iBACR,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,iBAAiB,CAAC,EACtC,WAAW,EACX,gBAAgB,EAChB,OAAO,GAKR;IAIC,4BAA4B;IAC5B,MAAM,UAAU,GAAc;QAC5B,IAAI,EAAE,EAAE;KACT,CAAC;IAEF,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;QACtC,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;QAErB,wEAAwE;QACxE,2EAA2E;QAC3E,SAAS;QACT,MAAM,iBAAiB,GAAG,MAAM,IAAA,6CAAoB,EAClD,MAAM,CAAC,0BAA0B,IAAI,GAAG,MAAM,CAAC,OAAO,cAAc,EACpE;YACE,OAAO;SACR,CACF,CAAC;QAEF,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,CAAC,QAAQ,GAAG,IAAA,6BAAY,EAAC,iBAAiB,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;YACpE,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CACtC,aAAa,CAAC,MAAM,EAAE,iBAAiB,EAAE,WAAW,CAAC,IAAI,CAAC,CAC3D,CAAC,CAAC,0BAA0B;QAC/B,CAAC;IACH,CAAC;IAED,OAAO;QACL,gBAAgB,EAAE,gBAA2C;QAC7D,UAAU;KACX,CAAC;AACJ,CAAC;AAED,SAAgB,gCAAgC,CAC9C,IAAY,EACZ,WAAwB;IAExB,8EAA8E;IAC9E,MAAM,oBAAoB,GAAG,IAAA,oBAAG,EAAC,WAAW,EAAE;QAC5C,kBAAkB;QAClB,yBAAyB;KAC1B,CAAC,CAAC;IACH,IACE,oBAAoB;QACpB,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,oBAAoB,EAAE;YACrD,iBAAiB,EAAE,IAAI;SACxB,CAAC,EACF,CAAC;QACD,kBAAQ,CAAC,IAAI,CACX,UAAU,IAAI,+CAA+C,aAAa,0CAA0C,oBAAoB,EAAE,CAC3I,CAAC;IACJ,CAAC;AACH,CAAC"}