"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeStaleJobs = void 0;
const manager_1 = require("../utils/jobs/manager");
const actions_1 = require("../redux/actions");
const removeStaleJobs = (jobs) => {
    const actions = [];
    // If any of our finished jobs are stale we remove them to keep our cache small
    jobs.complete.forEach((job, contentDigest) => {
        if ((0, manager_1.isJobStale)(job)) {
            actions.push(actions_1.internalActions.removeStaleJob(contentDigest));
        }
    });
    // If any of our pending jobs do not have an existing inputPath or the inputPath changed
    // we remove it from the queue as they would fail anyway
    jobs.incomplete.forEach(({ job }) => {
        if ((0, manager_1.isJobStale)(job)) {
            actions.push(actions_1.internalActions.removeStaleJob(job.contentDigest));
        }
        else {
            actions.push(actions_1.internalActions.createJobV2FromInternalJob(job));
        }
    });
    return actions;
};
exports.removeStaleJobs = removeStaleJobs;
//# sourceMappingURL=remove-stale-jobs.js.map