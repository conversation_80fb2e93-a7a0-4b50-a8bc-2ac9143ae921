"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.maybeAddFileProtocol = void 0;
exports.resolveJSFilepath = resolveJSFilepath;
const path_1 = __importDefault(require("path"));
const url_1 = require("url");
const reporter_1 = __importDefault(require("../reporter"));
/**
 * On Windows, the file protocol is required for the path to be resolved correctly.
 * On other platforms, the file protocol is not required, but supported, so we want to just always use it.
 * Except jest doesn't work with that and in that environment we never add the file protocol.
 */
exports.maybeAddFileProtocol = process.env.JEST_WORKER_ID
    ? (module) => module
    : (module) => (0, url_1.pathToFileURL)(module).href;
/**
 * Figure out if the file path is .js or .mjs without relying on the fs module, and return the file path if it exists.
 */
async function resolveJSFilepath({ rootDir, filePath, warn = true, }) {
    const filePathWithJSExtension = filePath.endsWith(`.js`)
        ? filePath
        : `${filePath}.js`;
    const filePathWithTSExtension = filePath.endsWith(`.ts`)
        ? filePath
        : `${filePath}.ts`;
    const filePathWithMJSExtension = filePath.endsWith(`.mjs`)
        ? filePath
        : `${filePath}.mjs`;
    // Check if both variants exist
    try {
        if (require.resolve(filePathWithJSExtension) &&
            require.resolve(filePathWithMJSExtension)) {
            if (warn) {
                reporter_1.default.warn(`The file '${path_1.default.relative(rootDir, filePath)}' has both .js and .mjs variants, please use one or the other. Using .js by default.`);
            }
            return filePathWithJSExtension;
        }
    }
    catch (_) {
        // Do nothing
    }
    // Check if .ts variant exists
    try {
        if (require.resolve(filePathWithTSExtension)) {
            return filePathWithTSExtension;
        }
    }
    catch (_) {
        // Do nothing
    }
    // Check if .js variant exists
    try {
        if (require.resolve(filePathWithJSExtension)) {
            return filePathWithJSExtension;
        }
    }
    catch (_) {
        // Do nothing
    }
    try {
        if (require.resolve(filePathWithMJSExtension)) {
            return filePathWithMJSExtension;
        }
    }
    catch (_) {
        // Do nothing
    }
    return ``;
}
//# sourceMappingURL=resolve-js-file-path.js.map