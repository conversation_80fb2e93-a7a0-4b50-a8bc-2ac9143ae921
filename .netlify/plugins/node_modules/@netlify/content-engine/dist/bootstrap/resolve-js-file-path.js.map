{"version": 3, "file": "resolve-js-file-path.js", "sourceRoot": "", "sources": ["../../src/bootstrap/resolve-js-file-path.ts"], "names": [], "mappings": ";;;;;;AAgBA,8CAkEC;AAlFD,gDAAwB;AACxB,6BAAoC;AACpC,2DAAiC;AAEjC;;;;GAIG;AACU,QAAA,oBAAoB,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc;IAC5D,CAAC,CAAC,CAAC,MAAc,EAAU,EAAE,CAAC,MAAM;IACpC,CAAC,CAAC,CAAC,MAAc,EAAU,EAAE,CAAC,IAAA,mBAAa,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAE3D;;GAEG;AACI,KAAK,UAAU,iBAAiB,CAAC,EACtC,OAAO,EACP,QAAQ,EACR,IAAI,GAAG,IAAI,GAKZ;IACC,MAAM,uBAAuB,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;QACtD,CAAC,CAAC,QAAQ;QACV,CAAC,CAAC,GAAG,QAAQ,KAAK,CAAC;IACrB,MAAM,uBAAuB,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;QACtD,CAAC,CAAC,QAAQ;QACV,CAAC,CAAC,GAAG,QAAQ,KAAK,CAAC;IACrB,MAAM,wBAAwB,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;QACxD,CAAC,CAAC,QAAQ;QACV,CAAC,CAAC,GAAG,QAAQ,MAAM,CAAC;IAEtB,+BAA+B;IAC/B,IAAI,CAAC;QACH,IACE,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC;YACxC,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,EACzC,CAAC;YACD,IAAI,IAAI,EAAE,CAAC;gBACT,kBAAM,CAAC,IAAI,CACT,aAAa,cAAI,CAAC,QAAQ,CACxB,OAAO,EACP,QAAQ,CACT,sFAAsF,CACxF,CAAC;YACJ,CAAC;YACD,OAAO,uBAAuB,CAAC;QACjC,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,aAAa;IACf,CAAC;IAED,8BAA8B;IAC9B,IAAI,CAAC;QACH,IAAI,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAC7C,OAAO,uBAAuB,CAAC;QACjC,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,aAAa;IACf,CAAC;IAED,8BAA8B;IAC9B,IAAI,CAAC;QACH,IAAI,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAC7C,OAAO,uBAAuB,CAAC;QACjC,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,aAAa;IACf,CAAC;IAED,IAAI,CAAC;QACH,IAAI,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,EAAE,CAAC;YAC9C,OAAO,wBAAwB,CAAC;QAClC,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,aAAa;IACf,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC"}