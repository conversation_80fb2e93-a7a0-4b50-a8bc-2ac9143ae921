"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveModuleExports = resolveModuleExports;
const dynamic_import_1 = require("../../lib/dynamic-import");
const reporter_1 = __importDefault(require("../reporter"));
const test_import_error_1 = require("../utils/test-import-error");
const resolve_js_file_path_1 = require("./resolve-js-file-path");
const prefer_default_1 = require("./prefer-default");
/**
 * Given a path to a module, return an array of the module's exports.
 *
 * It can run in two modes:
 * 1. `analysis` mode gets exports via static analysis by traversing the file's AST with babel
 * 2. `import` mode gets exports by directly importing the module and accessing its properties
 *
 * At the time of writing, analysis mode is used for files that can be jsx (e.g. gatsby-browser, gatsby-ssr)
 * and import mode is used for files that can be js or mjs.
 *
 * Returns [] for invalid paths and modules without exports.
 */
async function resolveModuleExports(modulePath, { rootDir = process.cwd() } = {}) {
    try {
        const moduleFilePath = await (0, resolve_js_file_path_1.resolveJSFilepath)({
            rootDir,
            filePath: modulePath,
        });
        if (!moduleFilePath) {
            return [];
        }
        const filePath = (0, resolve_js_file_path_1.maybeAddFileProtocol)(moduleFilePath);
        const rawImportedModule = await (0, dynamic_import_1.dynamicImport)(filePath);
        // If the module is cjs, the properties we care about are nested under a top-level `default` property
        const importedModule = (0, prefer_default_1.preferDefault)(rawImportedModule);
        return Object.keys(importedModule).filter((exportName) => exportName !== `__esModule`);
    }
    catch (error) {
        if (!(0, test_import_error_1.testImportError)(modulePath, error)) {
            // if module exists, but requiring it cause errors,
            // show the error to the user and terminate build
            reporter_1.default.panic(`Error in "${modulePath}":`, error);
        }
    }
    return [];
}
//# sourceMappingURL=resolve-module-exports.js.map