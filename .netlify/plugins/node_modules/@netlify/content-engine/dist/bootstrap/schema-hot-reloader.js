"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.bootstrapSchemaHotReloader = bootstrapSchemaHotReloader;
exports.startSchemaHotReloader = startSchemaHotReloader;
exports.stopSchemaHotReloader = stopSchemaHotReloader;
const lodash_clonedeep_1 = __importDefault(require("lodash.clonedeep"));
const lodash_debounce_1 = __importDefault(require("lodash.debounce"));
const redux_1 = require("../redux");
const schema_1 = require("../schema");
const inference_metadata_1 = require("../schema/infer/inference-metadata");
const reporter_1 = __importDefault(require("../reporter"));
const inferredTypesChanged = (typeMap, prevTypeMap) => Object.keys(typeMap).some((type) => typeMap[type].dirty && !(0, inference_metadata_1.haveEqualFields)(typeMap[type], prevTypeMap[type]));
let lastMetadata;
// API_RUNNING_QUEUE_EMPTY could be emitted multiple types
// in a short period of time, so debounce seems reasonable
const maybeRebuildSchema = (0, lodash_debounce_1.default)(async () => {
    const { inferenceMetadata } = redux_1.store.getState();
    if (!inferredTypesChanged(inferenceMetadata.typeMap, lastMetadata.typeMap)) {
        return;
    }
    const activity = reporter_1.default.activityTimer(`rebuild schema`);
    activity.start();
    await (0, schema_1.rebuild)({ parentSpan: activity });
    activity.end();
}, 1000);
function snapshotInferenceMetadata() {
    const { inferenceMetadata } = redux_1.store.getState();
    lastMetadata = (0, lodash_clonedeep_1.default)(inferenceMetadata);
}
function bootstrapSchemaHotReloader() {
    // Snapshot inference metadata at the time of the last schema rebuild
    // (even if schema was rebuilt elsewhere)
    // Using the snapshot later to check if inferred types actually changed since the last rebuild
    snapshotInferenceMetadata();
    redux_1.emitter.on(`SET_SCHEMA`, snapshotInferenceMetadata);
    startSchemaHotReloader();
}
function startSchemaHotReloader() {
    // Listen for node changes outside of a regular sourceNodes API call,
    // e.g. markdown file update via watcher
    redux_1.emitter.on(`API_RUNNING_QUEUE_EMPTY`, maybeRebuildSchema);
}
function stopSchemaHotReloader() {
    redux_1.emitter.off(`API_RUNNING_QUEUE_EMPTY`, maybeRebuildSchema);
    maybeRebuildSchema.cancel();
}
//# sourceMappingURL=schema-hot-reloader.js.map