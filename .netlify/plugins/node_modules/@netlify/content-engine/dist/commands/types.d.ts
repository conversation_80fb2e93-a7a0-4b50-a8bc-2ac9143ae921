import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "../types";
export interface ICert {
    key: string;
    cert: string;
}
export interface IDebugInfo {
    port: number;
    break: boolean;
}
export interface IProgram {
    open?: boolean;
    openTracingConfigFile: string;
    port: number;
    host: string;
    reporter: Reporter;
    [`cert-file`]?: string;
    [`key-file`]?: string;
    directory: string;
    https?: boolean;
    sitePackageJson: PackageJson;
    ssl?: ICert;
    inspect?: number;
    inspectBrk?: number;
    graphqlTracing?: boolean;
    verbose?: boolean;
    prefixPaths?: boolean;
    debugInfo?: IDebugInfo | null;
}
export declare enum Stage {
    Develop = "develop"
}
//# sourceMappingURL=types.d.ts.map