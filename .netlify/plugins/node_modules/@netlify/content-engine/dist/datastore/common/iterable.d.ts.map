{"version": 3, "file": "iterable.d.ts", "sourceRoot": "", "sources": ["../../../src/datastore/common/iterable.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,CAAC;IAEpB,SAAgB,gBAAgB,IAAI,IAAI,CAAC;CAC1C;AAGD;;;;;;;;GAQG;AACH,qBAAa,cAAc,CAAC,CAAC;IACf,OAAO,CAAC,MAAM;gBAAN,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE5D,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC;IAkBjC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;IAIpD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;IAI7D,MAAM,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC;IAG3D,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,GAAG,CAAC;IACrD,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC;IAczE,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC;IAQrD,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC;IAI7D,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,OAAO,GAAG,IAAI;IAO7D;;;;;OAKG;IACH,WAAW,CAAC,CAAC,GAAG,CAAC,EACf,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,EAClB,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,MAAM,GAC1C,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;IAIxB;;;;OAIG;IACH,eAAe,CAAC,CAAC,GAAG,CAAC,EACnB,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,EAClB,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,MAAM,GAC1C,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;IAIxB;;;;;;;;OAQG;IACH,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC;CAG1E;AAED;;GAEG;AACH,wBAAgB,UAAU,CAAC,GAAG,EAAE,OAAO,GAAG,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAK7D;AAED,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,CAE1E"}