{"version": 3, "file": "iterable.js", "sourceRoot": "", "sources": ["../../../src/datastore/common/iterable.ts"], "names": [], "mappings": ";;;AA2HA,gCAKC;AAED,gDAEC;AA/HD,+BAAwC;AACxC;;;;;;;;GAQG;AACH,MAAa,cAAc;IACL;IAApB,YAAoB,MAAyC;QAAzC,WAAM,GAAN,MAAM,CAAmC;IAAG,CAAC;IAEjE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;QAChB,MAAM,MAAM,GACV,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAElE,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,MAAM,GAAG,CAAC;YAEV,qFAAqF;YACrF,gFAAgF;YAChF,oCAAoC;YACpC,yDAAyD;YACzD,IAAI,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;gBACpB,IAAA,uBAAgB,GAAE,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,CAAI,KAAkB;QAC1B,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,GAAG,CAAI,EAAkC;QACvC,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,CAAC,SAAgC;QACrC,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IACnE,CAAC;IAGD,MAAM,CAAI,EAA0C,EAAE,YAAgB;QACpE,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,GAAG,GAAG,YAAiB,CAAC;QAC5B,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE,CAAC;gBAC1C,GAAG,GAAG,KAAqB,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,KAAK,CAAC,KAAa,EAAE,GAAY;QAC/B,IAAI,CAAC,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC;YAC1D,MAAM,IAAI,KAAK,CACb,wEAAwE,CACzE,CAAC;QACJ,OAAO,IAAI,cAAc,CAAI,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,WAAW,CAAC,KAA6B;QACvC,OAAO,IAAI,cAAc,CAAI,GAAG,EAAE,CAAC,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,OAAO,CAAC,QAA8C;QACpD,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC;YACzB,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,WAAW,CACT,KAAkB,EAClB,UAA2C;QAE3C,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;;;OAIG;IACH,eAAe,CACb,KAAkB,EAClB,UAA2C;QAE3C,OAAO,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;OAQG;IACH,iBAAiB,CAAC,UAAmC;QACnD,OAAO,IAAI,cAAc,CAAI,GAAG,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;IAC1E,CAAC;CACF;AAvGD,wCAuGC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,GAAY;IACrC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;QAC5C,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,OAAO,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC;AACpD,CAAC;AAED,SAAgB,kBAAkB,CAAI,KAAc;IAClD,OAAO,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpD,CAAC;AAED,QAAQ,CAAC,CAAC,WAAW,CACnB,MAAmB,EACnB,EAAgC;IAEhC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC;AACH,CAAC;AAED,QAAQ,CAAC,CAAC,aAAa,CACrB,MAAmB,EACnB,KAAa,EACb,GAAuB;IAEvB,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;IACf,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;QAC1B,KAAK,EAAE,CAAC;QACR,IAAI,KAAK,GAAG,KAAK;YAAE,SAAS;QAC5B,IAAI,OAAO,GAAG,KAAK,WAAW,IAAI,KAAK,IAAI,GAAG;YAAE,MAAM;QACtD,MAAM,IAAI,CAAC;IACb,CAAC;AACH,CAAC;AAED,QAAQ,CAAC,CAAC,cAAc,CACtB,MAAmB,EACnB,SAA8B;IAE9B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YACrB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;AACH,CAAC;AAED,QAAQ,CAAC,CAAC,cAAc,CACtB,KAAkB,EAClB,MAAmB;IAEnB,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC;QAC1B,MAAM,KAAK,CAAC;IACd,CAAC;IACD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,QAAQ,CAAC,CAAC,mBAAmB,CAC3B,MAAmB,EACnB,KAA6B;IAE7B,iEAAiE;IACjE,MAAM,UAAU,GAAG,IAAI,GAAG,EAAW,CAAC;IAEtC,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;QAC7B,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACzB,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACpB,MAAM,OAAO,CAAC;QAChB,CAAC;IACH,CAAC;AACH,CAAC;AAED,QAAQ,CAAC,CAAC,iBAAiB,CACzB,MAAmB,EACnB,aAAqC,iBAAiB;IAEtD,IAAI,IAAI,CAAC;IACT,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;QAC7B,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACnE,MAAM,OAAO,CAAC;QAChB,CAAC;QACD,IAAI,GAAG,OAAO,CAAC;IACjB,CAAC;AACH,CAAC;AAED,yCAAyC;AACzC,QAAQ,CAAC,CAAC,WAAW,CACnB,WAAwB,EACxB,YAAyB,EACzB,aAA6C,iBAAiB;IAE9D,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IAC7C,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IAC9C,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACrB,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,CAAC,KAAK,CAAC;gBACd,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,CAAC,KAAK,CAAC;gBACd,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;QACD,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,CAAC,CAAC,KAAK,CAAC;YACd,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC;QACD,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,CAAC,CAAC,KAAK,CAAC;YACd,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;YAAS,CAAC;QACT,iEAAiE;QACjE,6HAA6H;QAC7H,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU;YAAE,KAAK,CAAC,MAAM,EAAE,CAAC;QACvD,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU;YAAE,KAAK,CAAC,MAAM,EAAE,CAAC;IACzD,CAAC;AACH,CAAC;AAED,QAAQ,CAAC,CAAC,eAAe,CACvB,WAAwB,EACxB,YAAyB,EACzB,aAA6C,iBAAiB;IAE9D,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IAC7C,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IAC9C,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAErB,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC1B,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;YAExC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;gBACX,QAAQ;gBACR,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC;iBAAM,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;gBAClB,QAAQ;gBACR,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,CAAC,KAAK,CAAC;gBACd,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;YAAS,CAAC;QACT,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU;YAAE,KAAK,CAAC,MAAM,EAAE,CAAC;QACvD,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU;YAAE,KAAK,CAAC,MAAM,EAAE,CAAC;IACzD,CAAC;AACH,CAAC;AAED,SAAS,iBAAiB,CAAW,CAAQ,EAAE,CAAQ;IACrD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACZ,OAAO,CAAC,CAAC;IACX,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC"}