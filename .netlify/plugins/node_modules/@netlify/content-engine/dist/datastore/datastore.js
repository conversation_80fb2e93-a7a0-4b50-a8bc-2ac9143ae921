"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDataStore = getDataStore;
const create_require_from_path_1 = require("./../core-utils/create-require-from-path");
const redux_1 = require("../redux");
const dataStores = new Map();
function getDataStore(dbPath) {
    const dbName = dbPath || `default`;
    if (dataStores.has(dbName)) {
        return dataStores.get(dbName);
    }
    let usedStore;
    if (process.env.IN_MEMORY_DATASTORE) {
        const { setupInMemoryStore } = require(`./in-memory/in-memory-datastore`);
        usedStore = setupInMemoryStore();
    }
    else {
        const { setupLmdbStore } = require(`./lmdb/setup-datastore`);
        usedStore = setupLmdbStore({
            dbPath,
        });
    }
    dataStores.set(dbName, usedStore);
    return dataStores.get(dbName);
}
const gatsbyRedux = (0, create_require_from_path_1.checkIfGatsbyIsOverridden)()
    ? {}
    : (0, create_require_from_path_1.requireFromInstalledGatsbyDist)(`redux/index.js`);
// this will log twice if we're hooking into the gatsby emitter
if (!gatsbyRedux?.emitter) {
    // It is possible that the store is not initialized yet when calling `DELETE_CACHE`.
    //  The code below ensures we wipe cache from the proper store
    //  (mostly relevant for tests)
    redux_1.emitter.on(`DELETE_CACHE`, (action) => {
        getDataStore(action.dbPath);
    });
}
//# sourceMappingURL=datastore.js.map