"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupInMemoryStore = setupInMemoryStore;
const redux_1 = require("../../redux");
const iterable_1 = require("../common/iterable");
const run_fast_filters_1 = require("./run-fast-filters");
/**
 * @deprecated
 */
function getNodes() {
    const nodes = redux_1.store.getState().nodes ?? new Map();
    return Array.from(nodes.values());
}
/**
 * @deprecated
 */
function getNodesByType(type) {
    const nodes = redux_1.store.getState().nodesByType.get(type) ?? new Map();
    return Array.from(nodes.values());
}
function iterateNodes() {
    const nodes = redux_1.store.getState().nodes ?? new Map();
    return new iterable_1.GatsbyIterable(nodes.values());
}
function iterateNodesByType(type) {
    const nodes = redux_1.store.getState().nodesByType.get(type) ?? new Map();
    return new iterable_1.GatsbyIterable(nodes.values());
}
function getNode(id) {
    return redux_1.store.getState().nodes.get(id);
}
function getTypes() {
    // Note: sorting to match the output of the LMDB version (where keys are sorted by default)
    return Array.from(redux_1.store.getState().nodesByType.keys()).sort();
}
function countNodes(typeName) {
    if (!typeName) {
        return redux_1.store.getState().nodes.size;
    }
    const nodes = redux_1.store.getState().nodesByType.get(typeName);
    return nodes ? nodes.size : 0;
}
function runQuery(args) {
    return Promise.resolve((0, run_fast_filters_1.runFastFiltersAndSort)(args));
}
const readyPromise = Promise.resolve(undefined);
/**
 * Returns promise that resolves when the store is ready for reads
 * (the in-memory store is always ready)
 */
function ready() {
    return readyPromise;
}
function setupInMemoryStore() {
    return {
        resetCache() {
            return Promise.resolve();
        },
        clearIndexes: () => {
            console.warn("attempted to clear indexes for the in memory data store. this store does not have indexes");
        },
        getNode,
        getTypes,
        countNodes,
        ready,
        iterateNodes,
        iterateNodesByType,
        runQuery,
        // deprecated:
        getNodes,
        getNodesByType,
    };
}
//# sourceMappingURL=in-memory-datastore.js.map