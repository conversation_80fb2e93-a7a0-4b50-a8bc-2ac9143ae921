import { IGatsbyNode } from "../redux/types";
export { getDataStore } from "./datastore";
/**
 * Get all nodes from datastore.
 * @deprecated
 */
export declare const getNodes: () => Array<IGatsbyNode>;
/**
 * Get node by id from datastore.
 */
export declare const getNode: (id: string) => IGatsbyNode | undefined;
/**
 * Get all nodes of type from datastore.
 * @deprecated
 */
export declare const getNodesByType: (type: string) => Array<IGatsbyNode>;
/**
 * Get all type names from datastore.
 */
export declare const getTypes: () => Array<string>;
//# sourceMappingURL=index.d.ts.map