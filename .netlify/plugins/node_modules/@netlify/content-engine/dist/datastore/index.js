"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTypes = exports.getNodesByType = exports.getNode = exports.getNodes = exports.getDataStore = void 0;
const datastore_1 = require("./datastore");
var datastore_2 = require("./datastore");
Object.defineProperty(exports, "getDataStore", { enumerable: true, get: function () { return datastore_2.getDataStore; } });
// Convenience accessor methods
/**
 * Get all nodes from datastore.
 * @deprecated
 */
const getNodes = () => (0, datastore_1.getDataStore)().getNodes();
exports.getNodes = getNodes;
/**
 * Get node by id from datastore.
 */
const getNode = (id) => (0, datastore_1.getDataStore)().getNode(id);
exports.getNode = getNode;
/**
 * Get all nodes of type from datastore.
 * @deprecated
 */
const getNodesByType = (type) => (0, datastore_1.getDataStore)().getNodesByType(type);
exports.getNodesByType = getNodesByType;
/**
 * Get all type names from datastore.
 */
const getTypes = () => (0, datastore_1.getDataStore)().getTypes();
exports.getTypes = getTypes;
//# sourceMappingURL=index.js.map