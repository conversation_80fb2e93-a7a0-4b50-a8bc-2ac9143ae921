{"version": 3, "file": "lmdb-datastore.js", "sourceRoot": "", "sources": ["../../../src/datastore/lmdb/lmdb-datastore.ts"], "names": [], "mappings": ";;AAyBA,4CAYC;AAID,oCAoSC;AA7UD,+BAA6D;AAG7D,2CAA8C;AAC9C,2DAA4D;AAE5D,iDAAoD;AACpD,iDAA+C;AAC/C,oEAGuC;AACvC,uCAA8B;AAC9B,uCAAoC;AAYpC,SAAgB,gBAAgB,CAAC,MAAc,OAAO,CAAC,GAAG,EAAE;IAC1D,MAAM,UAAU,GACd,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;QAC7B,CAAC,CAAC,kBAAkB;QAChB,6EAA6E;QAC7E,uFAAuF;QACvF,0DAA0D;QAC1D,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,OAAO,CAAC,GAAG,CAAC,cACpD,EAAE;QACJ,CAAC,CAAC,WAAW,CAAC;IAElB,OAAO,GAAG,GAAG,eAAe,GAAG,UAAU,CAAC;AAC5C,CAAC;AAED,MAAM,MAAM,GAAG,IAAI,GAAG,EAAe,CAAC;AAEtC,SAAgB,YAAY,CAAC,EAC3B,MAAM,GAAG,gBAAgB,EAAE,MACJ,EAAE;IACzB,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;QAAE,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAElD,MAAM,aAAa,GAAG;QACpB,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,kBAAkB;QAClB,eAAe;QACf,KAAK;QACL,QAAQ;QACR,YAAY;QAEZ,cAAc;QACd,QAAQ;QACR,cAAc;KACf,CAAC;IAEF,MAAM,0BAA0B,GAAG,IAAI,GAAG,EAAE,CAAC;IAE7C,IAAI,MAAM,CAAC;IACX,IAAI,SAAS,CAAC;IAEd,SAAS,SAAS;QAChB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE,CAAC;gBACzC,UAAU,CAAC,wBAAwB,GAAG,IAAI,GAAG,EAAE,CAAC;YAClD,CAAC;YACD,MAAM,GAAG,UAAU,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACzD,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,GAAG,IAAA,WAAI,EAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;YAEH,UAAU,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,YAAY;QACnB,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,2EAA2E;YAC3E,0EAA0E;YAC1E,0EAA0E;YAC1E,+EAA+E;YAC/E,2DAA2D;YAC3D,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC;gBACpC,UAAU,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;YAC7C,CAAC;YACD,SAAS,GAAG,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACvD,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,GAAG,SAAS,EAAE,CAAC;YACrB,SAAS,GAAG;gBACV,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC;oBACnB,IAAI,EAAE,OAAO;oBACb,8FAA8F;oBAC9F,iDAAiD;oBACjD,KAAK,EAAE;wBACL,kEAAkE;wBAClE,gEAAgE;wBAChE,wCAAwC;wBACxC,OAAO,EAAE,KAAK;qBACf;iBACF,CAAC;gBACF,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC;oBACzB,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE,IAAI;iBACd,CAAC;gBACF,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC;oBACtB,IAAI,EAAE,UAAU;oBAChB,WAAW,EAAE,IAAI;iBAClB,CAAC;gBACF,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC;oBACrB,IAAI,EAAE,SAAS;oBACf,+FAA+F;oBAC/F,gBAAgB;iBACjB,CAAC;aACH,CAAC;YACF,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,SAAS,QAAQ;QACf,kCAAkC;QAClC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAc,YAAY,EAAE,CAAC,CAAC;QACvD,8CAA8C;QAC9C,gBAAgB;QAChB,+DAA+D;QAC/D,+DAA+D;QAC/D,IAAI;QACJ,OAAO,MAAM,IAAI,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,SAAS,cAAc,CAAC,IAAY;QAClC,kCAAkC;QAClC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAc,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,8CAA8C;QAC9C,gBAAgB;QAChB,2EAA2E;QAC3E,+DAA+D;QAC/D,IAAI;QACJ,OAAO,MAAM,IAAI,EAAE,CAAC;IACtB,CAAC;IAED,SAAS,YAAY;QACnB,iEAAiE;QACjE,MAAM,OAAO,GAAG,YAAY,EAAE,CAAC,KAAK,CAAC;QAErC,OAAO,IAAI,yBAAc,CACvB,OAAO;aACJ,OAAO,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;aAC5B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACd,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CACzD;aACA,MAAM,CAAC,OAAO,CAAmC,CACrD,CAAC;IACJ,CAAC;IAED,SAAS,kBAAkB,CAAC,IAAY;QACtC,MAAM,WAAW,GAAG,YAAY,EAAE,CAAC,WAAW,CAAC;QAE/C,OAAO,IAAI,yBAAc,CACvB,WAAW;aACR,SAAS,CAAC,IAAI,CAAC;aACf,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAChC,MAAM,CAAC,OAAO,CAAmC,CACrD,CAAC;IACJ,CAAC;IAED,SAAS,OAAO,CAAC,EAAU;QACzB,IAAI,CAAC,EAAE,IAAI,0BAA0B,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YAC9C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,YAAY,EAAE,CAAC;QACjC,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,QAAQ;QACf,OAAO,YAAY,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC;IACxD,CAAC;IAED,SAAS,UAAU,CAAC,QAAiB;QACnC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,YAAY,EAAE,CAAC,KAAK,CAAC,QAAQ,EAA4B,CAAC;YACxE,OAAO,IAAI,CAAC,GAAG,CACb,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,0BAA0B,CAAC,IAAI,EAC1D,CAAC,CACF,CAAC,CAAC,qDAAqD;QAC1D,CAAC;QAED,MAAM,EAAE,WAAW,EAAE,GAAG,YAAY,EAAE,CAAC;QACvC,OAAO,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,UAAU,QAAQ,CAAC,IAAmB;QACzC,IAAI,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,CAAC;YACjD,OAAO,MAAM,IAAA,sBAAU,EAAC;gBACtB,SAAS,EAAE,aAAa;gBACxB,SAAS,EAAE,YAAY,EAAE;gBACzB,GAAG,IAAI;aACR,CAAC,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,IAAA,wCAAqB,EAAC,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,UAAU,UAAU;QACvB,aAAK,CAAC,QAAQ,CAAC;YACb,IAAI,EAAE,cAAc;YACpB,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,IAAA,aAAE,EAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACnD,MAAM,UAAU,EAAE,CAAC;QACnB,MAAM,KAAK,EAAE,CAAC;IAChB,CAAC;IAED,KAAK,UAAU,UAAU;QACvB,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC;QAE3B,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE;YACjB,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE;YACvB,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;YACpB,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE;YACnB,MAAM,CAAC,KAAK,EAAE;SACf,CAAC,CAAC;QAEH,UAAU,CAAC,wBAAwB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACnD,UAAU,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,GAAG,SAAS,CAAC;QACnB,SAAS,GAAG,SAAS,CAAC;QACtB,YAAY,EAAE,CAAC;IACjB,CAAC;IAED,IAAI,oBAAoB,GAAiB,OAAO,CAAC,OAAO,EAAE,CAAC;IAE3D,SAAS,eAAe,CAAC,MAAoB;QAC3C,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC;gBAE3B,oBAAoB;gBACpB,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE;oBAC7B,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;oBACtB,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;oBAC5B,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACzB,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC1B,CAAC,CAAC,CAAC;gBAEH,MAAM;YACR,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,yEAAyE;gBACzE,YAAY,EAAE,CAAC;gBACf,MAAM;YACR,CAAC;YACD,KAAK,aAAa,CAAC;YACnB,KAAK,aAAa,CAAC;YACnB,KAAK,mBAAmB,CAAC;YACzB,KAAK,+BAA+B,CAAC,CAAC,CAAC;gBACrC,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC;gBAC3B,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC;oBACnC,IAAA,mBAAW,EAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;oBAC9B,IAAA,iCAAiB,EAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC;iBAC3C,CAAC,CAAC;gBACH,oBAAoB,GAAG,gBAAgB,CAAC;gBAExC,qFAAqF;gBACrF,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;oBAClC,0BAA0B,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACvD,CAAC;gBAED,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC;oBACxD,0BAA0B,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;oBAClD,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE;wBACzB,mEAAmE;wBACnE,IAAI,oBAAoB,KAAK,gBAAgB,EAAE,CAAC;4BAC9C,0BAA0B,CAAC,KAAK,EAAE,CAAC;wBACrC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS,YAAY;QACnB,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC;QAC3B,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE;YAC7B,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YACzB,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,UAAU,KAAK;QAClB,MAAM,oBAAoB,CAAC;IAC7B,CAAC;IAED,yEAAyE;IACzE,YAAY,EAAE,CAAC;IAEf,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAElC,OAAO,aAAa,CAAC;AACvB,CAAC"}