{"version": 3, "file": "create-index.js", "sourceRoot": "", "sources": ["../../../../src/datastore/lmdb/query/create-index.ts"], "names": [], "mappings": ";;;AA0CA,kCA0BC;AAED,4CAgBC;AAtFD,+BAA+B;AAC/B,0CAAuC;AAGvC,qCAA+D;AA0BlD,QAAA,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAY5C,KAAK,UAAU,WAAW,CAC/B,OAAyB,EACzB,QAAgB,EAChB,WAAwB;IAExB,MAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IACxD,MAAM,IAAI,GAAG,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAErE,QAAQ,IAAI,EAAE,KAAK,EAAE,CAAC;QACpB,KAAK,OAAO;YACV,OAAO,IAAI,CAAC;QACd,KAAK,UAAU,CAAC,CAAC,CAAC;YAChB,OAAO,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC;QACD,KAAK,SAAS,CAAC;QACf,OAAO,CAAC,CAAC,CAAC;YACR,IAAI,CAAC;gBACH,MAAM,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACtC,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,gDAAgD;gBAChD,iDAAiD;gBACjD,OAAO,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACxC,CAAC;YACD,OAAO,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAgB,gBAAgB,CAC9B,OAAyB,EACzB,QAAgB,EAChB,WAAwB,EACxB,WAAW,GAAG,IAAI;IAElB,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IAC9B,MAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IACxD,MAAM,IAAI,GAAmB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;IAE9E,IAAI,WAAW,IAAI,IAAI,EAAE,KAAK,KAAK,OAAO,EAAE,CAAC;QAC3C,MAAM,IAAI,KAAK,CACb,SAAS,SAAS,6BAA6B,IAAI,EAAE,KAAK,IAAI,SAAS,EAAE,CAC1E,CAAC;IACJ,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,KAAK,UAAU,aAAa,CAC1B,OAAyB,EACzB,QAAgB,EAChB,WAAwB;IAExB,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IACzC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;IACxC,MAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAExD,MAAM,KAAK,GAAG,YAAY,SAAS,EAAE,CAAC;IACtC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEpB,yDAAyD;IACzD,MAAM,aAAa,GAAG,aAAK,CAAC,QAAQ,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAExE,sCAAsC;IACtC,mCAAmC;IACnC,MAAM,KAAK,GAA4B;QACrC,cAAc,EAAE,CAAC;QACjB,QAAQ,EAAE,CAAC;QACX,SAAS,EAAE,CAAC;KACb,CAAC;IACF,MAAM,aAAa,GAAmB;QACpC,KAAK,EAAE,UAAU;QACjB,QAAQ;QACR,SAAS,EAAE,CAAC,GAAG,WAAW,CAAC;QAC3B,cAAc,EAAE,EAAE;QAClB,SAAS,EAAE,SAAS,EAAE,QAAQ;QAC9B,KAAK;KACN,CAAC;IAEF,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1D,0GAA0G;YAC1G,uEAAuE;YACvE,MAAM,cAAc,GAAG,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnD,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,gBAAgB,CAC/C,IAAI,EACJ,cAAc,EACd,SAAS,EACT,WAAW,CACZ,CAAC;YACF,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC;YAC9B,KAAK,CAAC,SAAS,EAAE,CAAC;YAClB,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACnE,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;YAErD,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,CAAC;gBAC5B,kGAAkG;gBAClG,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC;YACD,IAAI,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;gBACrB,mCAAmC;gBACnC,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QACD,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC;QAC9B,aAAa,CAAC,cAAc,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC;QAE1E,MAAM,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,aAAa,CAAC,CAAC;QAC5D,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAEvB,OAAO,aAAa,CAAC;IACvB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC;QAC9B,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,aAAa,CAAC,CAAC;QAC5D,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,SAAS,gBAAgB,CACvB,IAAiB,EACjB,cAAwD,EACxD,SAAiB,EACjB,WAAwB;IAExB,6CAA6C;IAC7C,MAAM,gBAAgB,GAAkC,EAAE,CAAC;IAC3D,MAAM,cAAc,GAAkB,EAAE,CAAC;IAEzC,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;IACnC,KAAK,MAAM,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;QAC7C,MAAM,UAAU,GAAG,IAAA,0BAAiB,EAAC,WAAW,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;QACxE,IAAI,eAAe,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAEnD,6CAA6C;QAC7C,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW,SAAS,IAAI,CAAC,EAAE,GAAG,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,gBAAgB,IAAI,KAAK,IAAA,cAAO,EAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAClE,CAAC;QACD,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC;YAC9C,CAAC,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,QAAQ;YACjC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QAEtB,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEvC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IACD,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAE/C,OAAO,EAAE,IAAI,EAAE,IAAA,yBAAgB,EAAC,GAAG,gBAAgB,CAAC,EAAE,cAAc,EAAE,CAAC;AACzE,CAAC;AAED,KAAK,UAAU,SAAS,CACtB,OAAyB,EACzB,SAAiB;IAEjB,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC;IACvC,MAAM,QAAQ,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;IAE1C,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE;QAC1D,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,OAAyB,EACzB,SAAiB;IAEjB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC;QAEvC,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,SAAS,IAAI;YACX,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7D,IAAI,aAAa,EAAE,KAAK,KAAK,OAAO,EAAE,CAAC;gBACrC,OAAO,CAAC,aAAa,CAAC,CAAC;gBACvB,OAAO;YACT,CAAC;YACD,IAAI,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC;gBACrB,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,SAAS,yBAAyB,CAAC,CAAC,CAAC;gBAC/D,OAAO;YACT,CAAC;YACD,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC1B,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,GAAG,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,EAAE,CAAC;IACT,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAS,cAAc,CAAC,QAAgB,EAAE,MAAmB;IAC3D,MAAM,MAAM,GAAkB,CAAC,QAAQ,CAAC,CAAC;IAEzC,KAAK,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,IAAI,MAAM,EAAE,CAAC;QAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,aAAa,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,aAAa,CAAC,SAAiB;IACtC,OAAO,SAAS,SAAS,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAc;IACtC,IACE,OAAO,KAAK,KAAK,QAAQ;QACzB,OAAO,KAAK,KAAK,QAAQ;QACzB,OAAO,KAAK,KAAK,SAAS;QAC1B,KAAK,KAAK,IAAI,EACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,oEAAoE;QACpE,0DAA0D;QAC1D,gFAAgF;QAChF,qFAAqF;QACrF,qCAAqC;QACrC,OAAO,uBAAe,CAAC;IACzB,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,MAAM,MAAM,GAA2B,EAAE,CAAC;QAC1C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE,CAAC;gBACnC,OAAO,SAAS,CAAC,CAAC,YAAY;YAChC,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,kEAAkE;IAClE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}