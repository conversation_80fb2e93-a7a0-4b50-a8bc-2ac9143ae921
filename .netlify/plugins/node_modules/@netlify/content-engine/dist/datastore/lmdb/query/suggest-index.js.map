{"version": 3, "file": "suggest-index.js", "sourceRoot": "", "sources": ["../../../../src/datastore/lmdb/query/suggest-index.ts"], "names": [], "mappings": ";;AA2BA,oCA0CC;AApED,8CAQ4B;AAC5B,qCAAkC;AAWlC;;;;;GAKG;AACH,SAAgB,YAAY,CAAC,EAC3B,MAAM,EACN,IAAI,EACJ,SAAS,GAAG,CAAC,GACI;IACjB,MAAM,aAAa,GAAG,IAAA,iCAAyB,EAAC,IAAA,wBAAgB,EAAC,MAAM,CAAC,CAAC,CAAC;IAC1E,MAAM,4BAA4B,GAAG,yBAAyB,CAAC,aAAa,CAAC,CAAC;IAC9E,MAAM,UAAU,GAAsB,4BAA4B,CAAC,IAAI,CAAC,CAAC;IAEzE,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,CAAC;QAC/D,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,CAAC;QACzC,OAAO,aAAa,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC;IACD,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACvB,OAAO,aAAa,CAClB,aAAa,CAAC,4BAA4B,CAAC,EAC3C,SAAS,CACV,CAAC;IACJ,CAAC;IAED,6FAA6F;IAC7F,qHAAqH;IACrH,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,MAAM,eAAe,GAAG,YAAY,CAAC,4BAA4B,CAAC,CAAC;IACnE,MAAM,cAAc,GAAG,aAAa,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;IAErE,0EAA0E;IAC1E,MAAM,OAAO,GAAG,qBAAqB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;IAEnE,OAAO,aAAa,CAClB;QACE,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxD,GAAG,UAAU;QACb,6FAA6F;QAC7F,gFAAgF;QAChF,oGAAoG;QACpG,GAAG,aAAa,CAAC,4BAA4B,EAAE,aAAa,CAAC;KAC9D,EACD,SAAS,CACV,CAAC;AACJ,CAAC;AAED,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC;IAC1B,oBAAY,CAAC,EAAE;IACf,oBAAY,CAAC,EAAE;IACf,oBAAY,CAAC,GAAG;IAChB,oBAAY,CAAC,GAAG;IAChB,oBAAY,CAAC,EAAE;IACf,oBAAY,CAAC,EAAE;IACf,oBAAY,CAAC,GAAG;IAChB,oBAAY,CAAC,EAAE;CAChB,CAAC,CAAC;AAEH;;;GAGG;AACH,SAAS,yBAAyB,CAAC,GAAmB;IACpD,OAAO,IAAA,yBAAiB,EACtB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,IAAA,0BAAkB,EAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CACrE,CAAC;AACJ,CAAC;AAED,SAAS,4BAA4B,CACnC,YAAgD;IAEhD,MAAM,IAAI,GAAG,YAAY,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IACvD,MAAM,YAAY,GAAG,IAAA,eAAM,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,MAAM,UAAU,GAAsB,EAAE,CAAC;IACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,KAAK,GAAG,IAAA,eAAM,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;YAC3B,0DAA0D;YAC1D,2FAA2F;YAC3F,MAAM;QACR,CAAC;QACD,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,qBAAqB,CAC5B,aAA6B,EAC7B,UAA6B;IAE7B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;IAElC,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,UAAU,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CACpC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,4BAAoB,EAAC,CAAC,CAAC,KAAK,SAAS,CAC7C,CAAC;QACF,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM;QACR,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACzB,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,YAAY,CAAC,aAA6B;IACjD,OAAO,aAAa,CAAC,MAAM,CACzB,CAAC,WAAW,EAAE,EAAE,CACd,IAAA,0BAAkB,EAAC,WAAW,CAAC,CAAC,UAAU,KAAK,oBAAY,CAAC,EAAE,CACjE,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CACpB,OAAuB,EACvB,gBAAwB,CAAC;IAEzB,OAAO,OAAO,CAAC,GAAG,CAChB,CAAC,CAAC,EAAc,EAAE,CAAC,CAAC,IAAA,4BAAoB,EAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAC5D,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,MAAmB,EAAE,SAAiB;IAC3D,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAClD,CAAC"}