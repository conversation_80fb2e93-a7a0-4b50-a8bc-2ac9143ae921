"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupLmdbStore = setupLmdbStore;
const lmdb_datastore_1 = require("./lmdb-datastore");
const redux_1 = require("../../redux");
function setupLmdbStore({ dbPath, } = {}) {
    const dataStore = (0, lmdb_datastore_1.getLmdbStore)({ dbPath });
    (0, redux_1.replaceReducer)({
        nodes: (state = new Map(), action) => action.type === `DELETE_CACHE` ? new Map() : state,
        nodesByType: (state = new Map(), action) => action.type === `DELETE_CACHE` ? new Map() : state,
    });
    redux_1.emitter.on(`*`, (action) => {
        if (action) {
            dataStore.updateDataStore?.(action);
        }
    });
    return dataStore;
}
//# sourceMappingURL=setup-datastore.js.map