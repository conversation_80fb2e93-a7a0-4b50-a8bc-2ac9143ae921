interface InternalHooks {
    before: () => Promise<void>;
    after: () => Promise<void>;
}
export declare const frameworkHooks: {
    startup: InternalHooks;
    sourceNodes: InternalHooks;
    customizeSchema: InternalHooks;
    buildSchema: InternalHooks;
    sync: InternalHooks;
};
export interface FrameworkContext {
    [key: string]: unknown;
}
type HookFn = (args: {
    runAPI: () => Promise<unknown>;
    context: FrameworkContext;
}) => Promise<void>;
export declare function setFrameworkHook(name: keyof typeof frameworkHooks, fn: HookFn): void;
export {};
//# sourceMappingURL=index.d.ts.map