{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/framework-hooks/index.ts"], "names": [], "mappings": ";;;AAsEA,4CA2FC;AA5JY,QAAA,cAAc,GAMvB;IACF,OAAO,EAAE;QACP,MAAM,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;QACtB,KAAK,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;KACtB;IACD,WAAW,EAAE;QACX,MAAM,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;QACtB,KAAK,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;KACtB;IACD,eAAe,EAAE;QACf,MAAM,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;QACtB,KAAK,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;KACtB;IACD,WAAW,EAAE;QACX,MAAM,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;QACtB,KAAK,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;KACtB;IACD,IAAI,EAAE;QACJ,MAAM,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;QACtB,KAAK,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;KACtB;CACF,CAAC;AASF,MAAM,WAAW,GAAG;IAClB,OAAO,EAAE,EAAE;CACZ,CAAC;AAEF,OAAO,CAAC,EAAE,CACR,SAAS,EACT,CAAC,GAAoE,EAAE,EAAE;IACvE,IAAI,GAAG,EAAE,IAAI,KAAK,kCAAkC,EAAE,CAAC;QACrD,WAAW,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC;IAC1C,CAAC;AACH,CAAC,CACF,CAAC;AAEF,SAAS,mBAAmB;IAC1B,IAAI,GAAG,GAAsC,IAAI,CAAC;IAClD,IAAI,GAAG,GAAsC,IAAI,CAAC;IAElD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC9C,GAAG,GAAG,OAAO,CAAC;QACd,GAAG,GAAG,MAAM,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,OAAO;QACP,OAAO,EAAE,GAAG;QACZ,MAAM,EAAE,GAAG;KACZ,CAAC;AACJ,CAAC;AAED,SAAgB,gBAAgB,CAC9B,IAAiC,EACjC,EAAU;IAEV,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,CAAC,sBAAc,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,kBAAkB,CAAC,CAAC;IAC7D,CAAC;IAQD,MAAM,gBAAgB,GAIlB;QACF,MAAM,EAAE;YACN,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;SACd;QACD,KAAK,EAAE;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;SACd;QACD,WAAW,EAAE,IAAI;KAClB,CAAC;IAEF,MAAM,SAAS,GAAG;QAChB,GAAG,gBAAgB;KACpB,CAAC;IAEF,SAAS,MAAM;QACb,IAAI,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC7B,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,SAAS,CAAC,KAAK,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC;IAED,sBAAc,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,KAAK,IAAI,EAAE;QACvC,IAAI,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC9B,SAAS,CAAC,MAAM,GAAG,mBAAmB,EAAE,CAAC;gBACzC,SAAS,CAAC,KAAK,GAAG,mBAAmB,EAAE,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,mBAAmB,IAAI,4HAA4H,CACpJ,CAAC;YACJ,CAAC;YAED,SAAS,CAAC,WAAW,GAAG,EAAE,CAAC;gBACzB,MAAM;gBACN,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACf,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC/B,MAAM,GAAG,CAAC;YACZ,CAAC,CAAC,CAAC;YAEH,MAAM,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC;QACjC,CAAC;gBAAS,CAAC;YACT,SAAS,CAAC,MAAM,GAAG;gBACjB,GAAG,gBAAgB,CAAC,MAAM;aAC3B,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEF,sBAAc,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,EAAE;QACtC,IAAI,CAAC;YACH,IAAI,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC5B,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;YAED,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;gBAC1B,MAAM,SAAS,CAAC,WAAW,CAAC;YAC9B,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;YAC7B,SAAS,CAAC,KAAK,GAAG;gBAChB,GAAG,gBAAgB,CAAC,KAAK;aAC1B,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;AACJ,CAAC"}