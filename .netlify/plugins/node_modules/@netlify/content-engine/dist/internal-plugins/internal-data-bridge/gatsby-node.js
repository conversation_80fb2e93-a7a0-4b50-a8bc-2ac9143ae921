"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const moment_1 = __importDefault(require("moment"));
const lodash_pick_1 = __importDefault(require("lodash.pick"));
function transformPackageJson(json) {
    const transformDeps = (deps) => deps &&
        Object.entries(deps).map(([name, version]) => {
            return {
                name,
                version,
            };
        });
    json = (0, lodash_pick_1.default)(json, [
        `name`,
        `description`,
        `version`,
        `main`,
        `keywords`,
        `author`,
        `license`,
        `dependencies`,
        `devDependencies`,
        `peerDependencies`,
        `optionalDependecies`,
        `bundledDependecies`,
    ]);
    json.dependencies = transformDeps(json.dependencies);
    json.devDependencies = transformDeps(json.devDependencies);
    json.peerDependencies = transformDeps(json.peerDependencies);
    json.optionalDependecies = transformDeps(json.optionalDependecies);
    json.bundledDependecies = transformDeps(json.bundledDependecies);
    return json;
}
exports.sourceNodes = ({ createContentDigest, actions, store }) => {
    const { createNode } = actions;
    const { program, flattenedPlugins, config } = store.getState();
    flattenedPlugins.forEach((plugin) => {
        plugin.pluginFilepath = plugin.resolve;
        createNode({
            ...plugin,
            packageJson: transformPackageJson(require(`${plugin.resolve}/package.json`)),
            parent: null,
            children: [],
            internal: {
                contentDigest: createContentDigest(plugin),
                type: `SitePlugin`,
            },
        });
    });
    const createGatsbyConfigNode = (config = {}) => {
        // Delete plugins from the config as we add plugins above.
        const configCopy = { ...config };
        delete configCopy.plugins;
        const node = {
            siteMetadata: {
                ...configCopy.siteMetadata,
            },
            port: program.port,
            host: program.host,
            ...configCopy,
        };
        createNode({
            ...node,
            id: `Site`,
            parent: null,
            children: [],
            internal: {
                contentDigest: createContentDigest(node),
                type: `Site`,
            },
        });
    };
    createGatsbyConfigNode(config);
    const buildTime = (0, moment_1.default)()
        .subtract(process.uptime(), `seconds`)
        .startOf(`second`)
        .toJSON();
    const metadataNode = { buildTime };
    createNode({
        ...metadataNode,
        id: `SiteBuildMetadata`,
        parent: null,
        children: [],
        internal: {
            contentDigest: createContentDigest(metadataNode),
            type: `SiteBuildMetadata`,
        },
    });
};
//# sourceMappingURL=gatsby-node.js.map