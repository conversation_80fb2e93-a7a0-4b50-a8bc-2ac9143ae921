{"version": 3, "file": "gatsby-node.js", "sourceRoot": "", "sources": ["../../../src/internal-plugins/internal-data-bridge/gatsby-node.ts"], "names": [], "mappings": ";;;;;AAAA,oDAA4B;AAC5B,8DAA+B;AAE/B,SAAS,oBAAoB,CAAC,IAAI;IAChC,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,EAAE,CAC7B,IAAI;QACJ,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,EAAE;YAC3C,OAAO;gBACL,IAAI;gBACJ,OAAO;aACR,CAAC;QACJ,CAAC,CAAC,CAAC;IAEL,IAAI,GAAG,IAAA,qBAAI,EAAC,IAAI,EAAE;QAChB,MAAM;QACN,aAAa;QACb,SAAS;QACT,MAAM;QACN,UAAU;QACV,QAAQ;QACR,SAAS;QACT,cAAc;QACd,iBAAiB;QACjB,kBAAkB;QAClB,qBAAqB;QACrB,oBAAoB;KACrB,CAAC,CAAC;IACH,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACrD,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC3D,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC7D,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACnE,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAEjE,OAAO,IAAI,CAAC;AACd,CAAC;AAED,OAAO,CAAC,WAAW,GAAG,CAAC,EAAE,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE;IAChE,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;IAC/B,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;IAE/D,gBAAgB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QAClC,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC;QAEvC,UAAU,CAAC;YACT,GAAG,MAAM;YACT,WAAW,EAAE,oBAAoB,CAC/B,OAAO,CAAC,GAAG,MAAM,CAAC,OAAO,eAAe,CAAC,CAC1C;YACD,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE;gBACR,aAAa,EAAE,mBAAmB,CAAC,MAAM,CAAC;gBAC1C,IAAI,EAAE,YAAY;aACnB;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,sBAAsB,GAAG,CAAC,MAAM,GAAG,EAAE,EAAE,EAAE;QAC7C,0DAA0D;QAC1D,MAAM,UAAU,GAAQ,EAAE,GAAG,MAAM,EAAE,CAAC;QACtC,OAAO,UAAU,CAAC,OAAO,CAAC;QAC1B,MAAM,IAAI,GAAG;YACX,YAAY,EAAE;gBACZ,GAAG,UAAU,CAAC,YAAY;aAC3B;YACD,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,GAAG,UAAU;SACd,CAAC;QACF,UAAU,CAAC;YACT,GAAG,IAAI;YACP,EAAE,EAAE,MAAM;YACV,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE;gBACR,aAAa,EAAE,mBAAmB,CAAC,IAAI,CAAC;gBACxC,IAAI,EAAE,MAAM;aACb;SACF,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAE/B,MAAM,SAAS,GAAG,IAAA,gBAAM,GAAE;SACvB,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC;SACrC,OAAO,CAAC,QAAQ,CAAC;SACjB,MAAM,EAAE,CAAC;IAEZ,MAAM,YAAY,GAAG,EAAE,SAAS,EAAE,CAAC;IAEnC,UAAU,CAAC;QACT,GAAG,YAAY;QACf,EAAE,EAAE,mBAAmB;QACvB,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE;YACR,aAAa,EAAE,mBAAmB,CAAC,YAAY,CAAC;YAChD,IAAI,EAAE,mBAAmB;SAC1B;KACF,CAAC,CAAC;AACL,CAAC,CAAC"}