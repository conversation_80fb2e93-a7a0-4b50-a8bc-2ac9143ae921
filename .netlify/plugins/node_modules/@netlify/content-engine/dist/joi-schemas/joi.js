"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.nodeSchema = exports.gatsbyConfigSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const stripTrailingSlash = (chain) => chain.replace(/(\w)\/+$/, `$1`);
exports.gatsbyConfigSchema = joi_1.default.object()
    .keys({
    flags: joi_1.default.object(),
    polyfill: joi_1.default.boolean().default(true),
    siteMetadata: joi_1.default.object({
        siteUrl: stripTrailingSlash(joi_1.default.string()).uri(),
    }).unknown(),
    mapping: joi_1.default.object(),
    plugins: joi_1.default.array(),
    proxy: joi_1.default.array()
        .items(joi_1.default.object().keys({
        prefix: joi_1.default.string().required(),
        url: joi_1.default.string().required(),
    }))
        .single(),
    developMiddleware: joi_1.default.func(),
})
    // throws when both assetPrefix and pathPrefix are defined
    .when(joi_1.default.object({
    assetPrefix: joi_1.default.string().uri({
        allowRelative: true,
        relativeOnly: true,
    }),
    pathPrefix: joi_1.default.string()
        .uri({
        allowRelative: true,
        relativeOnly: true,
    })
        .default(``),
}), {
    then: joi_1.default.object({
        assetPrefix: joi_1.default.string()
            .uri({
            allowRelative: false,
        })
            .error(new Error(`assetPrefix must be an absolute URI when used with pathPrefix`)),
    }),
});
exports.nodeSchema = joi_1.default.object()
    .keys({
    id: joi_1.default.string().required(),
    children: joi_1.default.array().items(joi_1.default.string(), joi_1.default.object().forbidden()),
    parent: joi_1.default.string().allow(null),
    fields: joi_1.default.object(),
    internal: joi_1.default.object()
        .keys({
        contentDigest: joi_1.default.string().required(),
        mediaType: joi_1.default.string(),
        type: joi_1.default.string().required(),
        owner: joi_1.default.string().required(),
        fieldOwners: joi_1.default.object(),
        content: joi_1.default.string().allow(``),
        description: joi_1.default.string(),
        ignoreType: joi_1.default.boolean(),
        counter: joi_1.default.number(),
        contentFilePath: joi_1.default.string(),
    })
        .unknown(false), // Don't allow non-standard fields
})
    .unknown();
//# sourceMappingURL=joi.js.map