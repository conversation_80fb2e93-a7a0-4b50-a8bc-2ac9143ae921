{"version": 3, "file": "error-parser.js", "sourceRoot": "", "sources": ["../../src/query/error-parser.ts"], "names": [], "mappings": ";;;AAeA,MAAM,WAAW,GAAG,CAAC,EACnB,OAAO,EACP,QAAQ,GAAG,SAAS,EACpB,QAAQ,GAAG,SAAS,EACpB,KAAK,GAAG,SAAS,GACJ,EAAU,EAAE;IACzB,4DAA4D;IAC5D,+BAA+B;IAC/B,MAAM,QAAQ,GAAG;QACf;YACE,KAAK,EAAE,6DAA6D;YACpE,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,OAAO;oBACL,EAAE,EAAE,OAAO;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;wBACvB,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;wBACtB,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;qBACvB;iBACF,CAAC;YACJ,CAAC;SACF;QACD;YACE,KAAK,EACH,0EAA0E;YAC5E,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,OAAO;oBACL,EAAE,EAAE,OAAO;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;wBACvB,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;wBACtB,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;wBACnB,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;qBACvB;iBACF,CAAC;YACJ,CAAC;SACF;QACD;YACE,KAAK,EACH,8EAA8E;YAChF,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,OAAO;oBACL,EAAE,EAAE,OAAO;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;wBACvB,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;wBACnB,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;qBACpB;iBACF,CAAC;YACJ,CAAC;SACF;QACD;YACE,KAAK,EAAE,6CAA6C;YACpD,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,OAAO;oBACL,EAAE,EAAE,OAAO;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;wBACvB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;wBACf,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;qBACf;iBACF,CAAC;YACJ,CAAC;SACF;QACD;YACE,KAAK,EAAE,2CAA2C;YAClD,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,OAAO;oBACL,EAAE,EAAE,OAAO;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;wBACvB,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACd,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;qBAChB;iBACF,CAAC;YACJ,CAAC;SACF;QACD;YACE,KAAK,EAAE,iDAAiD;YACxD,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,OAAO;oBACL,EAAE,EAAE,OAAO;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;wBACvB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;qBAChB;iBACF,CAAC;YACJ,CAAC;SACF;QACD;YACE,KAAK,EAAE,uCAAuC;YAC9C,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,OAAO;oBACL,EAAE,EAAE,OAAO;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;wBACvB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;qBACnB;iBACF,CAAC;YACJ,CAAC;SACF;QACD;YACE,KAAK,EAAE,uDAAuD;YAC9D,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,OAAO;oBACL,EAAE,EAAE,OAAO;oBACX,OAAO,EAAE;wBACP,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;wBACvB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;wBAClB,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;qBACpB;iBACF,CAAC;YACJ,CAAC;SACF;QACD,wDAAwD;QACxD;YACE,KAAK,EAAE,WAAW;YAClB,EAAE,EAAE,CAAC,KAAK,EAAU,EAAE;gBACpB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;oBAC3B,OAAO;wBACL,EAAE,EAAE,OAAO;wBACX,KAAK,EAAE,mBAAmB;wBAC1B,OAAO,EAAE,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;qBACrC,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,OAAO;wBACL,EAAE,EAAE,OAAO;wBACX,OAAO,EAAE,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;qBACrC,CAAC;gBACJ,CAAC;YACH,CAAC;SACF;KACF,CAAC;IAEF,IAAI,UAAU,CAAC;IAEf,KAAK,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,QAAQ,EAAE,CAAC;QACrC,MAAM,OAAO,GAAG,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACtC,IAAI,OAAO,EAAE,CAAC;YACZ,UAAU,GAAG;gBACX,GAAG,EAAE,CAAC,OAAO,CAAC;gBACd,GAAG,EAAE,QAAQ,EAAE;gBACf,GAAG,EAAE,QAAQ,EAAE;aAChB,CAAC;YACF,MAAM;QACR,CAAC;IACH,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AAEF,kBAAe,WAAW,CAAC;AAQpB,MAAM,uBAAuB,GAAG,CACrC,gCAA2D,EAC3D,eAA+B,EACf,EAAE;IAClB,OAAO;QACL,IAAI,EACF,eAAe,CAAC,IAAI,GAAG,gCAAgC,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC;QACxE,MAAM,EACJ,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC;YACzB,CAAC,CAAC,gCAAgC,CAAC,KAAK,CAAC,MAAM;YAC/C,CAAC,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM;KAClC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,uBAAuB,2BAYlC"}