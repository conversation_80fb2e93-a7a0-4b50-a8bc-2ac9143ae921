"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCodeFrame = getCodeFrame;
const code_frame_1 = require("@babel/code-frame");
function getCodeFrame(query, line, column) {
    if (!line) {
        return query;
    }
    return (0, code_frame_1.codeFrameColumns)(query, {
        start: {
            line,
            column,
        },
    }, {
        linesAbove: 10,
        linesBelow: 10,
    });
}
//# sourceMappingURL=graphql-errors-codeframe.js.map