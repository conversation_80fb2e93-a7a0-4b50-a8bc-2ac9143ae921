type RelayGraphQLError = Error & {
    validationErrors?: Object;
};
export declare function multipleRootQueriesError(filePath: string, def: any, otherDef: any): {
    id: string;
    filePath: string;
    context: {
        name: any;
        otherName: any;
        beforeCodeFrame: any;
        afterCodeFrame: any;
    };
};
export declare function graphqlError(definitionsByName: Map<string, any>, error: Error | RelayGraphQLError): {
    formattedMessage: string;
    docName: string;
    message: string;
    codeBlock: any;
};
export declare function unknownFragmentError({ fragmentNames, filePath, definition, node, }: {
    fragmentNames: any;
    filePath: any;
    definition: any;
    node: any;
}): {
    id: string;
    filePath: any;
    context: {
        fragmentName: any;
        closestFragment: any;
        codeFrame: any;
    };
};
export declare function duplicateFragmentError({ name, leftDefinition, rightDefinition, }: {
    name: any;
    leftDefinition: any;
    rightDefinition: any;
}): {
    id: string;
    context: {
        fragmentName: any;
        leftFragment: {
            filePath: any;
            codeFrame: any;
        };
        rightFragment: {
            filePath: any;
            codeFrame: any;
        };
    };
};
export {};
//# sourceMappingURL=graphql-errors.d.ts.map