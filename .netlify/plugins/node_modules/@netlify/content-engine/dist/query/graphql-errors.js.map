{"version": 3, "file": "graphql-errors.js", "sourceRoot": "", "sources": ["../../src/query/graphql-errors.ts"], "names": [], "mappings": ";AAAA,QAAQ;;;;;AA8HR,4DAyEC;AAED,oCA6BC;AAED,oDA8CC;AAED,wDAmDC;AAzUD,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AAC/B,qCAOiB;AACjB,kDAAqD;AACrD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACjE,wEAAyC;AACzC,2DAAiC;AACjC,MAAM,EAAE,uBAAuB,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAC9D,yEAA0D;AAC1D,2DAAuD;AAIvD,yEAAyE;AACzE,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,QAAQ,GAGR;IACJ;QACE,sCAAsC;QACtC,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE;YACf,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;gBACtD,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACvB,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;KACF;IACD;QACE,0BAA0B;QAC1B,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE;YACf,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;gBACzD,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACvB,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;KACF;IACD;QACE,4BAA4B;QAC5B,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE;YACf,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;gBAC1D,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACvB,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;KACF;CACF,CAAC;AAEF,SAAS,cAAc,CAAC,QAAgB;IACtC,OAAO,GAAG,kBAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,kBAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC1E,CAAC;AAED,SAAS,WAAW,CAAC,OAAe,EAAE,QAAgB,EAAE,SAAiB;IACvE,OAAO,CACL,kBAAM,CAAC,WAAW,CAAA;MAChB,OAAO;;QAEL,cAAc,CAAC,QAAQ,CAAC;GAC7B,GAAG,OAAO,SAAS,IAAI,CACvB,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,KAAY;IAKhC,MAAM,QAAQ,GACZ,kGAAkG,CAAC;IACrG,IAAI,OAA+B,CAAC;IACpC,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAC5D,oEAAoE;QACpE,IAAI,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,SAAS;YAAE,QAAQ,CAAC,SAAS,EAAE,CAAC;QAC/D,CAAC,EAAE,AAAD,EAAG,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,GAAG,OAAO,CAAC;IAC9C,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAED,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAEzB,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AACzC,CAAC;AAED,SAAS,YAAY,CAAC,gBAAwB,EAAE,GAAQ;IACtD,IAAI,QAAQ,GAAgC,IAAI,CAAC;IACjD,IAAA,eAAK,EAAC,GAAG,EAAE;QACT,KAAK,CAAC,IAAI;YACR,IAAI,QAAQ;gBAAE,OAAO;YACrB,KAAK,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACxC,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,CAAC,KAAK;oBAAE,SAAS;gBACrB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;oBAAE,MAAM;YACxD,CAAC;QACH,CAAC;KACF,CAAC,CAAC;IACH,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,0BAA0B,CACjC,GAAQ,EACR,gBAAwB,EACxB,MAAa;IAEb,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,gBAAgB,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;IACpE,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,eAAK,EAAC,GAAG,CAAC,CAAC;IAEhD,sEAAsE;IACtE,kDAAkD;IAClD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,IAAA,qBAAW,EAAC,MAAM,EAAE,KAAM,CAAC,CAAC,IAAI,EAAE,CAAC;IACvE,OAAO,IAAA,uCAAY,EAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC3C,CAAC;AAED,SAAgB,wBAAwB,CACtC,QAAgB,EAChB,GAAQ,EACR,QAAa;IAEb,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;IAC5B,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;IACtC,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IACxD,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IAClE,MAAM,WAAW,GAAG,GAAG,IAAA,0BAAS,EAAC,IAAI,CAAC,MAAM,IAAA,wBAAU,EACpD,IAAA,0BAAS,EAAC,SAAS,CAAC,CACrB,EAAE,CAAC;IAEJ,uDAAuD;IACvD,6CAA6C;IAC7C,2BAA2B;IAC3B,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC;IAEtD,OAAO;QACL,EAAE,EAAE,OAAO;QACX,QAAQ;QACR,OAAO,EAAE;YACP,IAAI;YACJ,SAAS;YACT,eAAe,EAAE,IAAA,6BAAgB,EAC/B,kBAAM,CAAC,WAAW,CAAA;gBACV,SAAS;YACb,KAAK;;;;;gBAKD,IAAI;YACR,UAAU;;;;OAIf,EACC;gBACE,KAAK,EAAE;oBACL,MAAM,EAAE,CAAC;oBACT,IAAI,EAAE,CAAC;iBACR;aACF,EACD;gBACE,UAAU,EAAE,MAAM,CAAC,gBAAgB;gBACnC,aAAa;aACd,CACF;YACD,cAAc,EAAE,IAAA,6BAAgB,EAC9B,kBAAM,CAAC,WAAW,CAAA;gBACV,WAAW;YACf,KAAK;;;YAGL,UAAU;;;;OAIf,EACC;gBACE,KAAK,EAAE;oBACL,MAAM,EAAE,CAAC;oBACT,IAAI,EAAE,CAAC;iBACR;aACF,EACD;gBACE,UAAU,EAAE,MAAM,CAAC,gBAAgB;gBACnC,aAAa;aACd,CACF;SACF;KACF,CAAC;AACJ,CAAC;AAED,SAAgB,YAAY,CAC1B,iBAAmC,EACnC,KAAgC;IAEhC,IAAI,SAAS,CAAC;IACd,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IACjD,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAE/D,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;QACxB,SAAS,GAAG,0BAA0B,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QACnE,OAAO,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IAC3D,CAAC;IAED,IAAI,eAAe,GAAG;IACpB,OAAO,IAAI,KAAK,CAAC,OAAO;KACvB,CAAC;IAEJ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,EAAE,CAAC;QAClD,eAAe;YACb,2EAA2E;gBAC3E,yFAAyF,CAAC;IAC9F,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC9C,eAAe,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC;IACpD,CAAC;IAED,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AAC5E,CAAC;AAED,SAAgB,oBAAoB,CAAC,EACnC,aAAa,EACb,QAAQ,EACR,UAAU,EACV,IAAI,GACL;IACC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IAC7B,MAAM,eAAe,GAAG,aAAa;SAClC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACT,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACtD,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;SAC3B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;IAElD,IAAI,IAAI,CAAC;IACT,IAAI,CAAC;QACH,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;IAC1D,CAAC;IAAC,MAAM,CAAC;QACP,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,OAAO;QACL,EAAE,EAAE,OAAO;QACX,QAAQ;QACR,OAAO,EAAE;YACP,YAAY,EAAE,IAAI;YAClB,eAAe;YACf,SAAS,EAAE,IAAA,6BAAgB,EACzB,IAAI,EACJ;gBACE,KAAK,EAAE,uBAAuB,CAC5B,UAAU,CAAC,WAAW,EACtB,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAY,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CACjE;gBACD,GAAG,EAAE,uBAAuB,CAC1B,UAAU,CAAC,WAAW,EACtB,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAC/D;aACF,EACD;gBACE,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE,EAAE;aACf,CACF;SACF;KACF,CAAC;AACJ,CAAC;AAED,SAAgB,sBAAsB,CAAC,EACrC,IAAI,EACJ,cAAc,EACd,eAAe,GAChB;IACC,OAAO;QACL,EAAE,EAAE,OAAO;QACX,OAAO,EAAE;YACP,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE;gBACZ,QAAQ,EAAE,cAAc,CAAC,QAAQ;gBACjC,SAAS,EAAE,IAAA,6BAAgB,EACzB,cAAc,CAAC,IAAI,EACnB;oBACE,KAAK,EAAE,IAAA,qBAAW,EAChB,EAAE,IAAI,EAAE,cAAc,CAAC,IAAI,EAAY,EACvC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAClC;oBACD,GAAG,EAAE,IAAA,qBAAW,EACd,EAAE,IAAI,EAAE,cAAc,CAAC,IAAI,EAAY,EACvC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAChC;iBACF,EACD;oBACE,UAAU,EAAE,EAAE;oBACd,UAAU,EAAE,EAAE;iBACf,CACF;aACF;YACD,aAAa,EAAE;gBACb,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,SAAS,EAAE,IAAA,6BAAgB,EACzB,eAAe,CAAC,IAAI,EACpB;oBACE,KAAK,EAAE,IAAA,qBAAW,EAChB,EAAE,IAAI,EAAE,eAAe,CAAC,IAAI,EAAY,EACxC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CACnC;oBACD,GAAG,EAAE,IAAA,qBAAW,EACd,EAAE,IAAI,EAAE,eAAe,CAAC,IAAI,EAAY,EACxC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CACjC;iBACF,EACD;oBACE,UAAU,EAAE,EAAE;oBACd,UAAU,EAAE,EAAE;iBACf,CACF;aACF;SACF;KACF,CAAC;AACJ,CAAC"}