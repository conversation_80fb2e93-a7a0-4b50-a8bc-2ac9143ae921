"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const reporter_1 = __importDefault(require("../reporter"));
const utils_1 = require("./utils");
/**
 * Tracks and knows how to get a parent span for a particular
 *  point in query resolver for a particular query and path
 */
class GraphQLSpanTracer {
    parentActivity;
    activities;
    constructor(name, activityArgs) {
        this.parentActivity = reporter_1.default.phantomActivity(name, activityArgs);
        this.activities = new Map();
    }
    getParentActivity() {
        return this.parentActivity;
    }
    start() {
        this.parentActivity.start();
    }
    end() {
        this.activities.forEach((activity) => {
            activity.end();
        });
        this.parentActivity.end();
    }
    createResolverActivity(path, name) {
        let prev = path.prev;
        while (typeof prev?.key === `number`) {
            prev = prev.prev;
        }
        const parentSpan = this.getActivity(prev).span;
        const activity = reporter_1.default.phantomActivity(`GraphQL Resolver`, {
            parentSpan,
            tags: {
                field: name,
                path: (0, utils_1.pathToArray)(path).join(`.`),
            },
        });
        this.setActivity(path, activity);
        return activity;
    }
    getActivity(gqlPath) {
        const path = (0, utils_1.pathToArray)(gqlPath);
        let activity;
        if (path.length > 0) {
            activity = this.activities.get(path.join(`.`));
            if (activity) {
                return activity;
            }
        }
        return this.parentActivity;
    }
    setActivity(gqlPath, activity) {
        const path = (0, utils_1.pathToArray)(gqlPath);
        this.activities.set(path.join(`.`), activity);
    }
}
exports.default = GraphQLSpanTracer;
//# sourceMappingURL=graphql-span-tracer.js.map