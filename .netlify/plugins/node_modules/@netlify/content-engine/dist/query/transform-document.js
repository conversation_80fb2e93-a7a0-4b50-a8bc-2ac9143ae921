"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.tranformDocument = tranformDocument;
const graphql = __importStar(require("graphql"));
function extractEnumValues(value, acc = []) {
    let hasValue = false;
    if (value.kind === graphql.Kind.ENUM) {
        hasValue = true;
        acc.push(value.value);
    }
    else if (value.kind === graphql.Kind.LIST) {
        // list can be empty but it indicate that it is set at least
        hasValue = true;
        for (const listItem of value.values) {
            extractEnumValues(listItem, acc);
        }
    }
    return hasValue ? acc : undefined;
}
function isOldSortObject(props) {
    if (!props || typeof props !== `object` || Array.isArray(props)) {
        return false;
    }
    let hasFields = false;
    // skip if there any unexpected keys
    for (const [key, value] of Object.entries(props)) {
        if (key === `fields`) {
            if (value) {
                hasFields = true;
            }
        }
        else if (key !== `order`) {
            return false;
        }
    }
    return hasFields;
}
function pathSegmentsToAst(path, value) {
    return path.split(`___`).reduceRight((previousNode, fieldPathSegment) => {
        return {
            kind: graphql.Kind.OBJECT,
            fields: [
                {
                    kind: graphql.Kind.OBJECT_FIELD,
                    name: {
                        kind: graphql.Kind.NAME,
                        value: fieldPathSegment,
                    },
                    value: previousNode,
                },
            ],
        };
    }, {
        kind: graphql.Kind.ENUM,
        value,
    });
}
function processGraphQLQuery(query) {
    try {
        let hasChanged = false; // this is sort of a hack, but print changes formatting and we only want to use it when we have to
        const ast = typeof query === `string` ? graphql.parse(query) : query;
        graphql.visit(ast, {
            Argument(node) {
                if (node.name.value === `sort`) {
                    if (node.value.kind !== graphql.Kind.OBJECT) {
                        return;
                    }
                    // old style sort: `allX(sort: { fields: <something>, order?: </something> })
                    const props = {};
                    for (const field of node.value.fields) {
                        props[field.name.value] = extractEnumValues(field.value);
                    }
                    if (!isOldSortObject(props)) {
                        return;
                    }
                    // iterate over each pair of field and order and create new object style for each
                    const newObjects = [];
                    for (let i = 0; i < props.fields.length; i++) {
                        const field = props.fields[i];
                        const order = props.order?.[i] ?? `ASC`;
                        newObjects.push(pathSegmentsToAst(field, order));
                    }
                    if (newObjects.length === 0) {
                        return;
                    }
                    // @ts-ignore node.value apparently is read-only ...
                    node.value =
                        newObjects.length > 1
                            ? {
                                kind: graphql.Kind.LIST,
                                values: newObjects,
                            }
                            : newObjects[0];
                    hasChanged = true;
                }
                else if (node.name.value === `field`) {
                    if (node.value.kind !== graphql.Kind.ENUM) {
                        return;
                    }
                    // @ts-ignore read-only ...
                    node.value = pathSegmentsToAst(node.value.value, `SELECT`);
                    hasChanged = true;
                }
            },
        });
        return { ast, hasChanged };
    }
    catch (err) {
        throw new Error(`GatsbySortAndAggrCodemod: GraphQL syntax error in query:\n\n${query}\n\nmessage:\n\n${err}`);
    }
}
function tranformDocument(ast) {
    try {
        return processGraphQLQuery(ast);
    }
    catch (error) {
        return { ast, hasChanged: false, error };
    }
}
//# sourceMappingURL=transform-document.js.map