{"version": 3, "file": "transform-document.js", "sourceRoot": "", "sources": ["../../src/query/transform-document.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA4IA,4CAUC;AAtJD,iDAAmC;AAEnC,SAAS,iBAAiB,CACxB,KAAwB,EACxB,MAAqB,EAAE;IAEvB,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACrC,QAAQ,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC5C,4DAA4D;QAC5D,QAAQ,GAAG,IAAI,CAAC;QAChB,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACpC,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IACD,OAAO,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AACpC,CAAC;AAOD,SAAS,eAAe,CAAC,KAAc;IACrC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAChE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,oCAAoC;IACpC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACjD,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;YACrB,IAAI,KAAK,EAAE,CAAC;gBACV,SAAS,GAAG,IAAI,CAAC;YACnB,CAAC;QACH,CAAC;aAAM,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,iBAAiB,CACxB,IAAY,EACZ,KAAa;IAEb,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,WAAW,CAClC,CAAC,YAAY,EAAE,gBAAgB,EAAE,EAAE;QACjC,OAAO;YACL,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM;YACzB,MAAM,EAAE;gBACN;oBACE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,YAAY;oBAC/B,IAAI,EAAE;wBACJ,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI;wBACvB,KAAK,EAAE,gBAAgB;qBACxB;oBACD,KAAK,EAAE,YAAY;iBACpB;aACF;SACF,CAAC;IACJ,CAAC,EACD;QACE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI;QACvB,KAAK;KAC6C,CACrD,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAoC;IAI/D,IAAI,CAAC;QACH,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,kGAAkG;QAC1H,MAAM,GAAG,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAErE,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;YACjB,QAAQ,CAAC,IAAI;gBACX,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;oBAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;wBAC5C,OAAO;oBACT,CAAC;oBAED,6EAA6E;oBAC7E,MAAM,KAAK,GAA8C,EAAE,CAAC;oBAC5D,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBACtC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC3D,CAAC;oBAED,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC5B,OAAO;oBACT,CAAC;oBAED,iFAAiF;oBACjF,MAAM,UAAU,GAEZ,EAAE,CAAC;oBACP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBAC7C,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBAC9B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;wBAExC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACnD,CAAC;oBAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC5B,OAAO;oBACT,CAAC;oBAED,oDAAoD;oBACpD,IAAI,CAAC,KAAK;wBACR,UAAU,CAAC,MAAM,GAAG,CAAC;4BACnB,CAAC,CAAC;gCACE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI;gCACvB,MAAM,EAAE,UAAU;6BACnB;4BACH,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACpB,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;oBACvC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;wBAC1C,OAAO;oBACT,CAAC;oBAED,2BAA2B;oBAC3B,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;oBAC3D,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QACH,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;IAC7B,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CACb,+DAA+D,KAAK,mBAAmB,GAAG,EAAE,CAC7F,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAgB,gBAAgB,CAAC,GAAyB;IAKxD,IAAI,CAAC;QACH,OAAO,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IAC3C,CAAC;AACH,CAAC"}