import * as internalActions from "./internal";
import { actions as publicActions } from "./public";
import { actions as restrictedActions } from "./restricted";
export declare const actions: {
    addThirdPartySchema: ({ schema }: {
        schema: import("graphql").GraphQLSchema;
    }, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IAddThirdPartySchema;
    createTypes: (types: string | import("graphql").GraphQLOutputType | import("../../schema/types/type-builders").GatsbyGraphQLType<any, any> | Array<string | import("graphql").GraphQLOutputType | import("../../schema/types/type-builders").GatsbyGraphQLType<any, any>>, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").ICreateTypes;
    createFieldExtension: (extension: import("../../schema/extensions").GraphQLFieldExtensionDefinition, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("redux-thunk").ThunkAction<void, import("../types").IGatsbyState, Record<string, unknown>, import("../types").ICreateFieldExtension>;
    printTypeDefinitions: ({ path, include, exclude, withFieldTypes, }: {
        path?: string;
        include?: {
            types?: Array<string>;
            plugins?: Array<string>;
        };
        exclude?: {
            types?: Array<string>;
            plugins?: Array<string>;
        };
        withFieldTypes?: boolean;
    }, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IPrintTypeDefinitions;
    createResolverContext: (context: import("../types").IGatsbyPluginContext, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("redux-thunk").ThunkAction<void, import("../types").IGatsbyState, Record<string, unknown>, import("../types").ICreateResolverContext>;
    createPageDependencies: (payload: Array<import("../types").ICreatePageDependencyActionPayloadType>, plugin?: string) => import("../types").ICreatePageDependencyAction;
    createPageDependency: (payload: import("../types").ICreatePageDependencyActionPayloadType, plugin?: string) => import("../types").ICreatePageDependencyAction;
    deleteComponentsDependencies: (paths: Array<string>) => import("../types").IDeleteComponentDependenciesAction;
    replaceComponentQuery: ({ query, componentPath, }: {
        query: string;
        componentPath: string;
    }) => import("../types").IReplaceComponentQueryAction;
    apiFinished: (payload: import("../types").IApiFinishedAction["payload"]) => import("../types").IApiFinishedAction;
    replaceStaticQuery: (args: {
        name: string;
        componentPath: string;
        id: string;
        query: string;
        hash: string;
    }, plugin?: import("../types").IGatsbyPlugin | null | undefined) => import("../types").IReplaceStaticQueryAction;
    queryExtracted: ({ componentPath, query }: {
        componentPath: string;
        query: string;
    }, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IQueryExtractedAction;
    setGraphQLDefinitions: (definitionsByName: Map<string, import("../types").IDefinitionMeta>) => import("../types").ISetGraphQLDefinitionsAction;
    queryExtractionGraphQLError: ({ componentPath, error }: {
        componentPath: string;
        error: string;
    }, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IQueryExtractionGraphQLErrorAction;
    queryExtractedBabelSuccess: ({ componentPath }: {
        componentPath: any;
    }, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IQueryExtractedBabelSuccessAction;
    queryExtractionBabelError: ({ componentPath, error }: {
        componentPath: string;
        error: Error;
    }, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IQueryExtractionBabelErrorAction;
    setProgramStatus: (status: import("../types").ProgramStatus, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").ISetProgramStatusAction;
    pageQueryRun: (payload: import("../types").IPageQueryRunAction["payload"], plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IPageQueryRunAction;
    queryStart: ({ path, componentPath, isPage }: {
        path: any;
        componentPath: any;
        isPage: any;
    }, plugin: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IQueryStartAction;
    clearDirtyQueriesListToEmitViaWebsocket: () => import("../types").IQueryClearDirtyQueriesListToEmitViaWebsocket;
    removeStaleJob: (contentDigest: string, plugin?: import("../types").IGatsbyPlugin, traceId?: string) => import("../types").IRemoveStaleJobAction;
    setSiteConfig: (config?: unknown) => import("../types").ISetSiteConfig;
    createJobV2FromInternalJob: (internalJob: import("../../utils/jobs/types").InternalJob) => import("../types").ICreateJobV2FromInternalAction;
    clearGatsbyImageSourceUrls: () => import("../types").IClearGatsbyImageSourceUrlAction;
};
export declare const restrictedActionsAvailableInAPI: {
    [x: string]: {
        createFieldExtension: import("redux").ActionCreator<import("../types").ActionsUnion> | import("redux").ActionCreator<import("redux-thunk").ThunkAction<any, import("../types").IGatsbyState, any, import("../types").ActionsUnion>>;
        createTypes: import("redux").ActionCreator<import("../types").ActionsUnion> | import("redux").ActionCreator<import("redux-thunk").ThunkAction<any, import("../types").IGatsbyState, any, import("../types").ActionsUnion>>;
        createResolverContext: import("redux").ActionCreator<import("../types").ActionsUnion> | import("redux").ActionCreator<import("redux-thunk").ThunkAction<any, import("../types").IGatsbyState, any, import("../types").ActionsUnion>>;
        addThirdPartySchema: import("redux").ActionCreator<import("../types").ActionsUnion> | import("redux").ActionCreator<import("redux-thunk").ThunkAction<any, import("../types").IGatsbyState, any, import("../types").ActionsUnion>>;
        printTypeDefinitions: import("redux").ActionCreator<import("../types").ActionsUnion> | import("redux").ActionCreator<import("redux-thunk").ThunkAction<any, import("../types").IGatsbyState, any, import("../types").ActionsUnion>>;
    };
};
export { internalActions, publicActions, restrictedActions };
//# sourceMappingURL=index.d.ts.map