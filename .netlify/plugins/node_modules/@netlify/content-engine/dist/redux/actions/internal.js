"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearGatsbyImageSourceUrls = exports.createJobV2FromInternalJob = exports.setSiteConfig = exports.removeStaleJob = exports.clearDirtyQueriesListToEmitViaWebsocket = exports.queryStart = exports.pageQueryRun = exports.setProgramStatus = exports.queryExtractionBabelError = exports.queryExtractedBabelSuccess = exports.queryExtractionGraphQLError = exports.setGraphQLDefinitions = exports.queryExtracted = exports.replaceStaticQuery = exports.apiFinished = exports.replaceComponentQuery = exports.deleteComponentsDependencies = exports.createPageDependency = exports.createPageDependencies = void 0;
const reporter_1 = __importDefault(require("../../reporter"));
const joi_1 = require("../../joi-schemas/joi");
const did_you_mean_1 = require("../../utils/did-you-mean");
const manager_1 = require("../../utils/jobs/manager");
const engine_context_1 = require("../../utils/engine-context");
/**
 * Create a dependency between a page and data. Probably for
 * internal use only.
 * @private
 */
const createPageDependencies = (payload, plugin = ``) => {
    return {
        type: `CREATE_COMPONENT_DEPENDENCY`,
        plugin,
        payload: payload.map(({ path, nodeId, connection }) => {
            return {
                path,
                nodeId,
                connection,
            };
        }),
    };
};
exports.createPageDependencies = createPageDependencies;
/**
 * Create a dependency between a page and data. Probably for
 * internal use only.
 *
 * Shorthand for createPageDependencies.
 * @private
 */
const createPageDependency = (payload, plugin = ``) => (0, exports.createPageDependencies)([payload], plugin);
exports.createPageDependency = createPageDependency;
/**
 * Delete dependencies between an array of pages and data. Probably for
 * internal use only. Used when deleting pages.
 * @private
 */
const deleteComponentsDependencies = (paths) => {
    return {
        type: `DELETE_COMPONENTS_DEPENDENCIES`,
        payload: {
            paths,
        },
    };
};
exports.deleteComponentsDependencies = deleteComponentsDependencies;
/**
 * When the query watcher extracts a GraphQL query, it calls
 * this to store the query with its component.
 * @private
 */
const replaceComponentQuery = ({ query, componentPath, }) => {
    return {
        type: `REPLACE_COMPONENT_QUERY`,
        payload: {
            query,
            componentPath,
        },
    };
};
exports.replaceComponentQuery = replaceComponentQuery;
const apiFinished = (payload) => {
    return {
        type: `API_FINISHED`,
        payload,
    };
};
exports.apiFinished = apiFinished;
/**
 * When the query watcher extracts a "static" GraphQL query from useStaticQuery
 * it calls this to store the query with its component.
 * @private
 */
const replaceStaticQuery = (args, plugin = null) => {
    return {
        type: `REPLACE_STATIC_QUERY`,
        plugin,
        payload: args,
    };
};
exports.replaceStaticQuery = replaceStaticQuery;
/**
 *
 * Report that a query has been extracted from a component. Used by
 * query-compiler.js.
 * @private
 */
const queryExtracted = ({ componentPath, query }, plugin, traceId) => {
    return {
        type: `QUERY_EXTRACTED`,
        plugin,
        traceId,
        payload: { componentPath, query },
    };
};
exports.queryExtracted = queryExtracted;
/**
 * Set Definitions for fragment extraction, etc.
 *
 * Used by developer tools such as vscode-graphql & graphiql
 *
 * query-compiler.js.
 * @private
 */
const setGraphQLDefinitions = (definitionsByName) => {
    return {
        type: `SET_GRAPHQL_DEFINITIONS`,
        payload: definitionsByName,
    };
};
exports.setGraphQLDefinitions = setGraphQLDefinitions;
/**
 *
 * Report that the Relay Compiler found a graphql error when attempting to extract a query
 * @private
 */
const queryExtractionGraphQLError = ({ componentPath, error }, plugin, traceId) => {
    return {
        type: `QUERY_EXTRACTION_GRAPHQL_ERROR`,
        plugin,
        traceId,
        payload: { componentPath, error },
    };
};
exports.queryExtractionGraphQLError = queryExtractionGraphQLError;
/**
 *
 * Report that babel was able to extract the graphql query.
 * Indicates that the file is free of JS errors.
 * @private
 */
const queryExtractedBabelSuccess = ({ componentPath }, plugin, traceId) => {
    return {
        type: `QUERY_EXTRACTION_BABEL_SUCCESS`,
        plugin,
        traceId,
        payload: { componentPath },
    };
};
exports.queryExtractedBabelSuccess = queryExtractedBabelSuccess;
/**
 *
 * Report that the Relay Compiler found a babel error when attempting to extract a query
 * @private
 */
const queryExtractionBabelError = ({ componentPath, error }, plugin, traceId) => {
    return {
        type: `QUERY_EXTRACTION_BABEL_ERROR`,
        plugin,
        traceId,
        payload: { componentPath, error },
    };
};
exports.queryExtractionBabelError = queryExtractionBabelError;
/**
 * Set overall program status e.g. `BOOTSTRAPING` or `BOOTSTRAP_FINISHED`.
 * @private
 */
const setProgramStatus = (status, plugin, traceId) => {
    return {
        type: `SET_PROGRAM_STATUS`,
        plugin,
        traceId,
        payload: status,
    };
};
exports.setProgramStatus = setProgramStatus;
/**
 * Broadcast that a page's query was run.
 * @private
 */
const pageQueryRun = (payload, plugin, traceId) => {
    return {
        type: `PAGE_QUERY_RUN`,
        plugin,
        traceId,
        payload,
    };
};
exports.pageQueryRun = pageQueryRun;
const queryStart = ({ path, componentPath, isPage }, plugin, traceId) => {
    return {
        type: `QUERY_START`,
        plugin,
        traceId,
        payload: { path, componentPath, isPage },
    };
};
exports.queryStart = queryStart;
const clearDirtyQueriesListToEmitViaWebsocket = () => {
    return {
        type: `QUERY_CLEAR_DIRTY_QUERIES_LIST_TO_EMIT_VIA_WEBSOCKET`,
    };
};
exports.clearDirtyQueriesListToEmitViaWebsocket = clearDirtyQueriesListToEmitViaWebsocket;
/**
 * Remove jobs which are marked as stale (inputPath doesn't exists)
 * @private
 */
const removeStaleJob = (contentDigest, plugin, traceId) => {
    return {
        type: `REMOVE_STALE_JOB_V2`,
        plugin,
        traceId,
        payload: {
            contentDigest,
        },
    };
};
exports.removeStaleJob = removeStaleJob;
/**
 * Set gatsby config
 * @private
 */
const setSiteConfig = (config) => {
    const result = joi_1.gatsbyConfigSchema.validate(config || {});
    const normalizedPayload = result.value;
    if (result.error) {
        const hasUnknownKeys = result.error.details.filter((details) => details.type === `object.unknown`);
        if (Array.isArray(hasUnknownKeys) && hasUnknownKeys.length) {
            const errorMessages = hasUnknownKeys.map((unknown) => {
                const { context, message } = unknown;
                const key = context?.key;
                const suggestion = key && (0, did_you_mean_1.didYouMean)(key);
                if (suggestion) {
                    return `${message}. ${suggestion}`;
                }
                return message;
            });
            reporter_1.default.panic({
                id: `10122`,
                context: {
                    sourceMessage: errorMessages.join(`\n`),
                },
            });
        }
        reporter_1.default.panic({
            id: `10122`,
            context: {
                sourceMessage: result.error.message,
            },
        });
    }
    return {
        type: `SET_SITE_CONFIG`,
        // payload: config,
        payload: normalizedPayload,
    };
};
exports.setSiteConfig = setSiteConfig;
const createJobV2FromInternalJob = (internalJob) => (dispatch, getState) => {
    const jobContentDigest = internalJob.contentDigest;
    const currentState = getState();
    // Check if we already ran this job before, if yes we return the result
    // We have an inflight (in progress) queue inside the jobs manager to make sure
    // we don't waste resources twice during the process
    if (currentState.jobsV2 &&
        currentState.jobsV2.complete.has(jobContentDigest)) {
        return Promise.resolve(currentState.jobsV2.complete.get(jobContentDigest).result);
    }
    const engineContext = (0, engine_context_1.getEngineContext)();
    // Always set context, even if engineContext is undefined.
    // We do this because the final list of jobs for a given engine request includes both:
    //  - jobs with the same requestId
    //  - jobs without requestId (technically with requestId === "")
    //
    // See https://nodejs.org/dist/latest-v16.x/docs/api/async_context.html#async_context_troubleshooting_context_loss
    // on cases when async context could be lost.
    dispatch({
        type: `SET_JOB_V2_CONTEXT`,
        payload: {
            job: internalJob,
            requestId: engineContext?.requestId ?? ``,
        },
    });
    const inProgressJobPromise = (0, manager_1.getInProcessJobPromise)(jobContentDigest);
    if (inProgressJobPromise) {
        return inProgressJobPromise;
    }
    dispatch({
        type: `CREATE_JOB_V2`,
        payload: {
            job: internalJob,
        },
        plugin: { name: internalJob.plugin.name },
    });
    const enqueuedJobPromise = (0, manager_1.enqueueJob)(internalJob);
    return enqueuedJobPromise.then((result) => {
        // store the result in redux so we have it for the next run
        dispatch({
            type: `END_JOB_V2`,
            plugin: { name: internalJob.plugin.name },
            payload: {
                jobContentDigest,
                result,
            },
        });
        // remove the job from our inProgressJobQueue as it's available in our done state.
        // this is a perf optimisations so we don't grow our memory too much when using gatsby preview
        (0, manager_1.removeInProgressJob)(jobContentDigest);
        return result;
    });
};
exports.createJobV2FromInternalJob = createJobV2FromInternalJob;
const clearGatsbyImageSourceUrls = () => {
    return {
        type: `CLEAR_GATSBY_IMAGE_SOURCE_URL`,
    };
};
exports.clearGatsbyImageSourceUrls = clearGatsbyImageSourceUrls;
//# sourceMappingURL=internal.js.map