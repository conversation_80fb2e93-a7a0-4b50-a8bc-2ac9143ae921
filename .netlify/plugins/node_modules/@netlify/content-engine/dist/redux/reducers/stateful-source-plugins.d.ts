import { IGatsbyState, IEnableStatefulSourcePluginAction } from "../types";
/**
 * Flags a source plugin as being "stateful" which means it manages its own data updates and Gatsby doesn't look for "stale" nodes after each `sourceNodes` run.
 */
export declare const statefulSourcePluginsReducer: (statefulSourcePlugins: IGatsbyState["statefulSourcePlugins"] | undefined, action: IEnableStatefulSourcePluginAction) => IGatsbyState["statefulSourcePlugins"];
//# sourceMappingURL=stateful-source-plugins.d.ts.map