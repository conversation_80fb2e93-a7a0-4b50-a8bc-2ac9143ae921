"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.statusReducer = void 0;
const lodash_merge_1 = __importDefault(require("lodash.merge"));
const is_object_1 = require("../../core-utils/is-object");
const defaultState = {
    PLUGINS_HASH: ``,
    LAST_NODE_COUNTER: 0,
    plugins: {},
};
const statusReducer = (state = defaultState, action) => {
    switch (action.type) {
        case `DELETE_CACHE`:
            return defaultState;
        case `UPDATE_PLUGINS_HASH`:
            return {
                ...state,
                PLUGINS_HASH: action.payload,
            };
        case `SET_PLUGIN_STATUS`:
            if (!action.plugin || !action.plugin?.name) {
                throw new Error(`You can't set plugin status without a plugin`);
            }
            if (!(0, is_object_1.isObject)(action.payload)) {
                throw new Error(`You must pass an object into setPluginStatus. What was passed in was ${JSON.stringify(action.payload, null, 4)}`);
            }
            return {
                ...state,
                plugins: {
                    ...state.plugins,
                    [action.plugin.name]: (0, lodash_merge_1.default)({}, state.plugins[action.plugin.name], action.payload),
                },
            };
        case `CREATE_NODE`:
            state.LAST_NODE_COUNTER = action.payload.internal.counter;
            return state;
        default:
            return state;
    }
};
exports.statusReducer = statusReducer;
//# sourceMappingURL=status.js.map