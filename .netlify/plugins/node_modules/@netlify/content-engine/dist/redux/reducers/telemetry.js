"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.telemetryReducer = void 0;
const initialState = {
    gatsbyImageSourceUrls: new Set(),
};
const telemetryReducer = (state = initialState, action) => {
    switch (action.type) {
        case `PROCESS_GATSBY_IMAGE_SOURCE_URL`: {
            const { sourceUrl } = action.payload;
            state.gatsbyImageSourceUrls.add(sourceUrl);
            return state;
        }
        case `CLEAR_GATSBY_IMAGE_SOURCE_URL`: {
            state.gatsbyImageSourceUrls = new Set();
            return state;
        }
        default: {
            return state;
        }
    }
};
exports.telemetryReducer = telemetryReducer;
//# sourceMappingURL=telemetry.js.map