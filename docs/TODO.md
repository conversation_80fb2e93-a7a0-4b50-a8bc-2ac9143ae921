# TODO

## DONE:

- [x] Create a back-end for the Latest Commitments signatures sections
- [x] Replace all example.com placeholders
- [x] Fix Netlify Blobs fallback store
- [x] Fix back-end (see [signatures.js](../netlify/functions/signatures.js))
- [x] Deploy this project

## TBD:

- [ ] Make the 'Latest Commitments' signatures section scalable with pagination
- [ ] Improve project structure
- [ ] Extend the API with a DELETE endpoint gated behind admin auth for removal requests
- [ ] Add a script that auto-populates the `.env` file, and adds/updates env. vars. in Netlify
