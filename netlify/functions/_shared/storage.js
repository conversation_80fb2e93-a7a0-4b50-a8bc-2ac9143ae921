const fs = require("node:fs/promises");
const path = require("node:path");
const { getStore } = require("@netlify/blobs");

const STORE_NAME = "signatures";
const MAX_SAFE_MILLIS = Number.MAX_SAFE_INTEGER;
const KEY_LENGTH = 12; // base36 padded length for inverted timestamp

const IS_NETLIFY_DEV = process.env.NETLIFY_DEV === "true";
const IS_TEST_ENV = process.env.NODE_ENV === "test";
const ALLOW_LOCAL_FALLBACK =
  IS_NETLIFY_DEV || IS_TEST_ENV || process.env.ENABLE_BLOBS_FALLBACK === "true";

const MANUAL_BLOBS_SITE_ID =
  process.env.NETLIFY_BLOBS_SITE_ID || process.env.NETLIFY_SITE_ID;
const MANUAL_BLOBS_TOKEN =
  process.env.NETLIFY_BLOBS_TOKEN ||
  process.env.NETLIFY_ACCESS_TOKEN ||
  process.env.NETLIFY_TOKEN;

const PROJECT_ROOT = IS_NETLIFY_DEV
  ? process.cwd()
  : path.resolve(__dirname, "../../..");
const FALLBACK_DIR = path.join(PROJECT_ROOT, ".netlify", "dev-blobs");
const FALLBACK_FILE = path.join(FALLBACK_DIR, `${STORE_NAME}.json`);

let storePromise;
let fallbackStore;

class LocalBlobStore {
  constructor(filePath) {
    this.filePath = filePath;
    this.items = new Map();
    this.loaded = false;
  }

  async prepare() {
    if (this.loaded) return;
    try {
      const raw = await fs.readFile(this.filePath, "utf8");
      const parsed = JSON.parse(raw);
      if (parsed && typeof parsed === "object") {
        for (const [key, value] of Object.entries(parsed)) {
          this.items.set(key, value);
        }
      }
    } catch (error) {
      if (error.code !== "ENOENT") {
        throw error;
      }
    }
    this.loaded = true;
  }

  async list({ limit = 100, cursor } = {}) {
    await this.prepare();
    const keys = Array.from(this.items.keys()).sort();

    let startIndex = 0;
    if (cursor) {
      const existingIndex = keys.indexOf(cursor);
      if (existingIndex >= 0) {
        startIndex = existingIndex + 1;
      }
    }

    const endIndex = limit ? startIndex + limit : keys.length;
    const selectedKeys = keys.slice(startIndex, endIndex);
    const blobs = selectedKeys.map((key) => ({ key }));
    const hasMore = endIndex < keys.length;
    const nextCursor = hasMore && selectedKeys.length
      ? selectedKeys[selectedKeys.length - 1]
      : undefined;

    return {
      blobs,
      cursor: nextCursor,
    };
  }

  async get(key) {
    await this.prepare();
    const value = this.items.get(key);
    return value ? JSON.stringify(value) : null;
  }

  async getJSON(key) {
    await this.prepare();
    return this.items.get(key) || null;
  }

  async setJSON(key, value) {
    await this.prepare();
    this.items.set(key, value);
    await fs.mkdir(path.dirname(this.filePath), { recursive: true });
    await fs.writeFile(
      this.filePath,
      JSON.stringify(Object.fromEntries(this.items), null, 2),
      "utf8",
    );
  }
}

function isMissingBlobsEnvironmentError(error) {
  if (!error) return false;
  return (
    error.name === "MissingBlobsEnvironmentError" ||
    error.message?.includes("Netlify Blobs")
  );
}

async function ensureFallbackStore() {
  if (!ALLOW_LOCAL_FALLBACK) {
    throw new Error(
      "Netlify Blobs environment is unavailable and local fallback is disabled. " +
        "Run `netlify dev` or enable Blobs on your site.",
    );
  }

  if (fallbackStore) {
    return fallbackStore;
  }

  fallbackStore = new LocalBlobStore(FALLBACK_FILE);
  await fallbackStore.prepare();

  console.warn("storage.fallback", {
    message:
      "Netlify Blobs credentials missing. Using local JSON store for development.",
    storePath: FALLBACK_FILE,
  });

  return fallbackStore;
}

async function createRemoteStore() {
  try {
    return await getStore({ name: STORE_NAME });
  } catch (error) {
    if (!isMissingBlobsEnvironmentError(error)) {
      throw error;
    }

    if (MANUAL_BLOBS_SITE_ID && MANUAL_BLOBS_TOKEN) {
      try {
        const manualStore = await getStore({
          name: STORE_NAME,
          siteID: MANUAL_BLOBS_SITE_ID,
          token: MANUAL_BLOBS_TOKEN,
        });

        console.info("storage.blobs.manual_credentials", {
          siteId: MANUAL_BLOBS_SITE_ID,
        });

        return manualStore;
      } catch (manualError) {
        manualError.message = `Unable to access Netlify Blobs store with provided credentials: ${manualError.message}`;
        manualError.name = manualError.name || "NetlifyBlobsAccessError";
        manualError.cause = manualError.cause || error;
        throw manualError;
      }
    }

    if (ALLOW_LOCAL_FALLBACK) {
      return ensureFallbackStore();
    }

    const guidanceError = new Error(
      "Netlify Blobs is not configured. Set NETLIFY_BLOBS_TOKEN (and optionally NETLIFY_BLOBS_SITE_ID) or run under Netlify Dev.",
    );
    guidanceError.name = "MissingBlobsEnvironmentError";
    guidanceError.cause = error;
    throw guidanceError;
  }
}

async function getStoreInstance() {
  if (!storePromise) {
    storePromise = createRemoteStore();
  }

  return storePromise;
}

function toBase36Padded(value) {
  return value.toString(36).padStart(KEY_LENGTH, "0");
}

function generateSignatureKey(timestampMs = Date.now(), id) {
  const inverted = MAX_SAFE_MILLIS - timestampMs;
  const timeComponent = toBase36Padded(inverted);
  return `${timeComponent}-${id}`;
}

function encodeCursor(rawCursor) {
  if (!rawCursor) return undefined;
  return Buffer.from(rawCursor, "utf8").toString("base64");
}

function decodeCursor(encodedCursor) {
  if (!encodedCursor) return undefined;
  try {
    return Buffer.from(encodedCursor, "base64").toString("utf8");
  } catch (error) {
    return undefined;
  }
}

async function listEntries({ limit, cursor } = {}) {
  const store = await getStoreInstance();
  const result = await store.list({
    limit,
    cursor,
  });

  const entries = await Promise.all(
    result.blobs.map(async (blob) => {
      try {
        const jsonString = await store.get(blob.key);
        if (!jsonString) return undefined;
        const data = JSON.parse(jsonString);
        return data ? { ...data } : undefined;
      } catch (error) {
        console.warn(`Failed to parse JSON for key ${blob.key}:`, error.message);
        return undefined;
      }
    }),
  );

  return {
    entries: entries.filter(Boolean),
    cursor: result.cursor,
  };
}

async function setEntry(key, value) {
  const store = await getStoreInstance();
  await store.setJSON(key, value);
}

module.exports = {
  STORE_NAME,
  getStoreInstance,
  generateSignatureKey,
  encodeCursor,
  decodeCursor,
  listEntries,
  setEntry,
  _internals: {
    LocalBlobStore,
    ensureFallbackStore,
    resetForTests() {
      storePromise = undefined;
      fallbackStore = undefined;
    },
    FALLBACK_FILE,
    isMissingBlobsEnvironmentError,
  },
};
