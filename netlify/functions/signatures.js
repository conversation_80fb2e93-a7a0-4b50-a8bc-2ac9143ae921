const crypto = require("crypto");

const { jsonResponse, noContentResponse } = require("./_shared/http");
const storage = require("./_shared/storage");
const validation = require("./_shared/validation");

const DEFAULT_LIMIT = 6;
const MAX_LIMIT = 25;
const RATE_LIMIT_PER_HOUR = 10;
const DUPLICATE_WINDOW_MS = 10 * 60 * 1000; // 10 minutes
const HOURLY_WINDOW_MS = 60 * 60 * 1000; // 1 hour
const EMAIL_DUPLICATE_WINDOW_MS = 24 * 60 * 60 * 1000; // 24 hours
const DUPLICATE_CHECK_PAGE_SIZE = 50;
const DUPLICATE_CHECK_MAX_PAGES = 6;

const SALT = process.env.SIGNATURES_SALT || "";
const ADMIN_TOKEN = process.env.SIGNATURES_ADMIN_TOKEN || "";

const internals = {};

function getOrigin(event) {
  return event.headers?.origin || event.headers?.Origin || undefined;
}

function parseLimit(rawLimit) {
  if (!rawLimit) return DEFAULT_LIMIT;
  const parsed = Number.parseInt(rawLimit, 10);
  if (Number.isNaN(parsed)) return null;
  return Math.min(Math.max(parsed, 1), MAX_LIMIT);
}

function extractAuthorization(headers = {}) {
  return headers.authorization || headers.Authorization || "";
}

function isAdminRequest(event) {
  const authHeader = extractAuthorization(event.headers);
  if (!ADMIN_TOKEN) return false;
  return authHeader === `Bearer ${ADMIN_TOKEN}`;
}

function sanitizeRecord(record, includePrivate = false) {
  const base = {
    id: record.id,
    name: record.name,
    role: record.role,
    timestamp: record.timestamp,
  };

  if (record.message) {
    base.message = record.message;
  }

  if (includePrivate) {
    base.email = record.email;
    base.ipHash = record.ipHash;
  }

  return base;
}

function getClientIp(event) {
  const headers = event.headers || {};
  const direct =
    headers["x-nf-client-connection-ip"] ||
    headers["client-ip"] ||
    headers["x-real-ip"];
  if (direct) return direct.split(",")[0].trim();

  const forwarded = headers["x-forwarded-for"];
  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }

  return event.ip || event.clientIp || "unknown";
}

function hashIp(ipAddress) {
  const hash = crypto.createHash("sha256");
  hash.update(`${SALT}|${ipAddress || "unknown"}`);
  return hash.digest("hex");
}

async function gatherRecentEntries({
  maxPages = DUPLICATE_CHECK_MAX_PAGES,
  pageSize = DUPLICATE_CHECK_PAGE_SIZE,
  maxAgeMs = EMAIL_DUPLICATE_WINDOW_MS,
} = {}) {
  const store = await storage.getStoreInstance();
  let cursor;
  const collected = [];
  let page = 0;
  const now = Date.now();

  while (page < maxPages) {
    const { blobs, cursor: nextCursor } = await store.list({
      limit: pageSize,
      cursor,
    });

    if (!blobs.length) {
      break;
    }

    const batch = await Promise.all(
      blobs.map(async (blob) => {
        try {
          const jsonString = await store.get(blob.key);
          if (!jsonString) return null;
          const data = JSON.parse(jsonString);
          return data || null;
        } catch (error) {
          console.warn(`Failed to parse JSON for key ${blob.key}:`, error.message);
          return null;
        }
      }),
    );

    for (const entry of batch) {
      if (entry) {
        collected.push(entry);
      }
    }

    const oldestEntry = batch[batch.length - 1];
    if (!nextCursor) {
      break;
    }

    if (
      oldestEntry &&
      typeof oldestEntry.timestamp === "string" &&
      now - Date.parse(oldestEntry.timestamp) > maxAgeMs
    ) {
      break;
    }

    cursor = nextCursor;
    page += 1;
  }

  return collected;
}

function buildErrorResponse(statusCode, message, origin, extras = {}) {
  return jsonResponse(
    statusCode,
    {
      errors: [
        {
          code: extras.code || "error",
          message,
          field: extras.field || null,
        },
      ],
    },
    { origin },
  );
}

function buildStorageErrorResponse(error, origin) {
  console.error("signatures.storage_error", {
    name: error?.name,
    message: error?.message,
    code: error?.code,
    status: error?.status,
    cause: error?.cause?.message,
    stack: error?.stack?.split('\n').slice(0, 3).join('\n'), // First 3 lines of stack
  });

  if (
    storage._internals?.isMissingBlobsEnvironmentError?.(error) ||
    error?.name === "NetlifyBlobsAccessError" ||
    error?.name === "MissingBlobsEnvironmentError"
  ) {
    return jsonResponse(
      503,
      {
        errors: [
          {
            code: "storage_unavailable",
            message:
              "Signatures storage is unavailable. Please enable Netlify Blobs for your site or configure NETLIFY_BLOBS_TOKEN and NETLIFY_BLOBS_SITE_ID environment variables.",
            details: {
              errorType: error?.name,
              suggestion: "Check Netlify dashboard for Blobs configuration or environment variables"
            }
          },
        ],
      },
      { origin },
    );
  }

  return buildErrorResponse(500, "Unable to access signatures storage.", origin, {
    code: "storage_error",
  });
}

async function handleGet(event, origin) {
  const params = event.queryStringParameters || {};
  const parsedLimit = parseLimit(params.limit);

  if (parsedLimit === null) {
    return buildErrorResponse(400, "limit must be an integer", origin, {
      code: "invalid_query",
      field: "limit",
    });
  }

  const decodedCursor = storage.decodeCursor(params.cursor);
  if (params.cursor && !decodedCursor) {
    return buildErrorResponse(400, "cursor is invalid", origin, {
      code: "invalid_query",
      field: "cursor",
    });
  }

  const wantsPrivateFields = params.fields === "all";
  if (wantsPrivateFields && !isAdminRequest(event)) {
    const unauthorized = buildErrorResponse(401, "Unauthorized", origin, {
      code: "unauthorized",
      field: "fields",
    });
    return {
      ...unauthorized,
      headers: {
        ...unauthorized.headers,
        "WWW-Authenticate": "Bearer realm=\"signatures\"",
      },
    };
  }

  let entries;
  let cursor;
  try {
    const result = await storage.listEntries({
      limit: parsedLimit,
      cursor: decodedCursor,
    });
    entries = result.entries;
    cursor = result.cursor;
  } catch (error) {
    return buildStorageErrorResponse(error, origin);
  }

  const responseEntries = entries.map((entry) =>
    sanitizeRecord(entry, wantsPrivateFields && isAdminRequest(event)),
  );

  return jsonResponse(
    200,
    {
      data: responseEntries,
      meta: {
        count: responseEntries.length,
        limit: parsedLimit,
        cursor: storage.encodeCursor(cursor) || null,
      },
    },
    { origin },
  );
}

async function handlePost(event, origin) {
  const contentType = event.headers?.["content-type"] || event.headers?.["Content-Type"];
  if (!contentType || !contentType.includes("application/json")) {
    return buildErrorResponse(415, "Content-Type must be application/json", origin, {
      code: "unsupported_media_type",
    });
  }

  let payload;
  try {
    payload = JSON.parse(event.body || "{}");
  } catch (_error) {
    return buildErrorResponse(400, "Request body must be valid JSON", origin, {
      code: "invalid_json",
    });
  }
  const validationResult = validation.validateSignaturePayload(payload);
  if (!validationResult.success) {
    return jsonResponse(
      400,
      {
        errors: validationResult.errors,
      },
      { origin },
    );
  }

  const data = validationResult.data;
  const ipAddress = getClientIp(event);
  const ipHash = hashIp(ipAddress);

  const now = Date.now();
  let recentEntries;
  try {
    recentEntries = await internals.gatherRecentEntries();
  } catch (error) {
    return buildStorageErrorResponse(error, origin);
  }

  const duplicatesWithinWindow = recentEntries.filter((entry) => {
    if (!entry.timestamp) return false;
    const entryTime = Date.parse(entry.timestamp);
    if (Number.isNaN(entryTime)) return false;
    return entry.ipHash === ipHash && now - entryTime <= DUPLICATE_WINDOW_MS;
  });

  if (duplicatesWithinWindow.length > 0) {
    return buildErrorResponse(429, "Please wait before submitting another commitment.", origin, {
      code: "rate_limited",
    });
  }

  const submissionsLastHour = recentEntries.filter((entry) => {
    if (!entry.timestamp) return false;
    const entryTime = Date.parse(entry.timestamp);
    if (Number.isNaN(entryTime)) return false;
    return entry.ipHash === ipHash && now - entryTime <= HOURLY_WINDOW_MS;
  });

  if (submissionsLastHour.length >= RATE_LIMIT_PER_HOUR) {
    return buildErrorResponse(429, "Too many submissions from this network. Please try later.", origin, {
      code: "rate_limited",
    });
  }

  const duplicateEmail = recentEntries.find((entry) => {
    if (!entry.timestamp) return false;
    const entryTime = Date.parse(entry.timestamp);
    if (Number.isNaN(entryTime)) return false;
    return (
      entry.email?.toLowerCase() === data.email.toLowerCase() &&
      now - entryTime <= EMAIL_DUPLICATE_WINDOW_MS
    );
  });

  if (duplicateEmail) {
    return buildErrorResponse(409, "This email has already signed recently.", origin, {
      code: "duplicate_submission",
      field: "email",
    });
  }

  const timestamp = new Date(now).toISOString();
  const signatureRecord = {
    id: crypto.randomUUID(),
    name: data.name,
    email: data.email,
    role: data.role,
    timestamp,
    ipHash,
  };

  if (data.message) {
    signatureRecord.message = data.message;
  }

  const key = storage.generateSignatureKey(now, signatureRecord.id);
  try {
    await storage.setEntry(key, signatureRecord);
  } catch (error) {
    return buildStorageErrorResponse(error, origin);
  }

  const responsePayload = {
    data: sanitizeRecord(signatureRecord),
  };

  return jsonResponse(201, responsePayload, { origin });
}

async function handler(event) {
  const origin = getOrigin(event);

  if (event.httpMethod === "OPTIONS") {
    return noContentResponse(origin);
  }

  try {
    if (event.httpMethod === "GET") {
      return await handleGet(event, origin);
    }

    if (event.httpMethod === "POST") {
      return await handlePost(event, origin);
    }

    return buildErrorResponse(405, "Method Not Allowed", origin, {
      code: "method_not_allowed",
    });
  } catch (error) {
    const errorId = crypto.randomUUID();
    console.error("signatures.unhandled", {
      errorId,
      message: error.message,
      stack: error.stack,
    });
    return jsonResponse(
      500,
      {
        errors: [
          {
            code: "internal_error",
            message: "An unexpected error occurred.",
            errorId,
          },
        ],
      },
      { origin },
    );
  }
}

internals.gatherRecentEntries = gatherRecentEntries;

exports.handler = handler;

module.exports = {
  handler,
  _internals: {
    handleGet,
    handlePost,
    sanitizeRecord,
    parseLimit,
    getClientIp,
    hashIp,
    gatherRecentEntries,
    isAdminRequest,
    internals,
  },
};
