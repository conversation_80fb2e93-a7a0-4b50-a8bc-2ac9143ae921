{"name": "@netlify/blobs", "version": "10.0.11", "description": "TypeScript client for Netlify Blobs", "type": "module", "engines": {"node": "^14.16.0 || >=16.0.0"}, "main": "./dist/main.cjs", "module": "./dist/main.js", "types": "./dist/main.d.ts", "exports": {".": {"require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}, "import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "default": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}}, "./package.json": "./package.json", "./server": {"require": {"types": "./dist/server.d.cts", "default": "./dist/server.cjs"}, "import": {"types": "./dist/server.d.ts", "default": "./dist/server.js"}, "default": {"types": "./dist/server.d.ts", "default": "./dist/server.js"}}}, "files": ["dist/**/*", "server.d.ts"], "scripts": {"build": "tsup-node", "dev": "tsup-node --watch", "prepack": "npm run build", "test": "run-s build test:ci", "test:dev": "run-s build test:dev:*", "test:ci": "run-s build test:ci:*", "test:dev:vitest": "vitest", "test:dev:vitest:watch": "vitest watch", "test:ci:vitest": "vitest run", "publint": "npx -y publint --strict"}, "keywords": [], "license": "MIT", "repository": "netlify/primitives", "bugs": {"url": "https://github.com/netlify/primitives/issues"}, "author": "Netlify Inc.", "directories": {"test": "test"}, "devDependencies": {"@types/node": "^18.19.110", "node-fetch": "^3.3.1", "npm-run-all2": "^8.0.4", "semver": "^7.5.3", "tmp-promise": "^3.0.3", "tsup": "^8.0.0", "vitest": "^3.0.0"}, "dependencies": {"@netlify/dev-utils": "4.2.0", "@netlify/runtime-utils": "2.1.0"}}