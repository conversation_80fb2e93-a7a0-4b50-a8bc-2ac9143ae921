const fs = require("fs");
const path = require("path");

// Load environment variables from .env if it exists
const envPath = path.join(process.cwd(), ".env");
if (fs.existsSync(envPath)) {
  const content = fs.readFileSync(envPath, "utf8");
  for (const line of content.split(/\r?\n/)) {
    const match = line.match(/^([^#=]+)=(.*)$/);
    if (match) {
      const key = match[1].trim();
      const value = match[2].trim();
      if (key && value !== undefined) {
        process.env[key] = value;
      }
    }
  }
}

const { getStore } = require("@netlify/blobs");

async function testBlobsMethods() {
  console.log("Testing Netlify Blobs methods...");
  
  try {
    const store = await getStore({
      name: "test-store",
      siteID: process.env.NETLIFY_BLOBS_SITE_ID,
      token: process.env.NETLIFY_BLOBS_TOKEN,
    });

    console.log("Store created successfully");
    console.log("Available methods:", Object.getOwnPropertyNames(Object.getPrototypeOf(store)));
    
    // Test basic methods
    console.log("Testing set method...");
    await store.set("test-key", "test-value");
    console.log("Set successful");
    
    console.log("Testing get method...");
    const value = await store.get("test-key");
    console.log("Got value:", value);
    
    console.log("Testing list method...");
    const list = await store.list({ limit: 1 });
    console.log("List result:", list);
    
  } catch (error) {
    console.error("Test failed with error:", error.message);
    console.error("Error name:", error.name);
    console.error("Error stack:", error.stack);
  }
}

testBlobsMethods();
