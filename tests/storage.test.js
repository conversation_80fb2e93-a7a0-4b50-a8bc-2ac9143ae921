process.env.SIGNATURES_SALT = "unit-test-salt";
process.env.NODE_ENV = "test";
process.env.NETLIFY_DEV = "true";
// Ensure tests use fallback store by clearing Netlify Blobs credentials
delete process.env.NETLIFY_BLOBS_SITE_ID;
delete process.env.NETLIFY_BLOBS_TOKEN;
delete process.env.NETLIFY_SITE_ID;
delete process.env.NETLIFY_ACCESS_TOKEN;
delete process.env.NETLIFY_TOKEN;

const fs = require("node:fs/promises");
const path = require("node:path");
const { test } = require("node:test");
const assert = require("node:assert/strict");

const storage = require("../netlify/functions/_shared/storage");

const {
  LocalBlobStore,
  ensureFallbackStore,
  resetForTests,
  FALLBACK_FILE,
} = storage._internals;

const FALLBACK_DIR = path.dirname(FALLBACK_FILE);

async function cleanFallbackFile() {
  await fs.rm(FALLBACK_FILE, { force: true });
}

test("LocalBlobStore persists items to disk", async (t) => {
  await cleanFallbackFile();
  resetForTests();

  const store = new LocalBlobStore(FALLBACK_FILE);
  await store.prepare();

  await store.setJSON("test-key", { value: 42 });
  const listing = await store.list({ limit: 10 });

  assert.equal(listing.blobs.length, 1);
  assert.equal(listing.cursor, undefined);
  assert.deepEqual(await store.getJSON("test-key"), { value: 42 });

  t.after(async () => {
    await cleanFallbackFile();
  });
});

test("storage listEntries uses fallback pagination", async (t) => {
  await cleanFallbackFile();
  await fs.mkdir(FALLBACK_DIR, { recursive: true });
  resetForTests();

  const fallback = await ensureFallbackStore();
  const now = Date.now();

  const newerKey = storage.generateSignatureKey(now, "newer");
  const olderKey = storage.generateSignatureKey(now - 60000, "older");

  await fallback.setJSON(newerKey, {
    id: "newer",
    timestamp: new Date(now).toISOString(),
  });

  await fallback.setJSON(olderKey, {
    id: "older",
    timestamp: new Date(now - 60000).toISOString(),
  });

  const firstPage = await storage.listEntries({ limit: 1 });
  assert.equal(firstPage.entries.length, 1);
  assert.equal(firstPage.entries[0].id, "newer");
  assert.ok(firstPage.cursor);

  const encoded = storage.encodeCursor(firstPage.cursor);
  const decoded = storage.decodeCursor(encoded);
  assert.equal(decoded, firstPage.cursor);

  const secondPage = await storage.listEntries({ limit: 1, cursor: decoded });
  assert.equal(secondPage.entries.length, 1);
  assert.equal(secondPage.entries[0].id, "older");
  assert.equal(secondPage.cursor, undefined);

  t.after(async () => {
    resetForTests();
    await cleanFallbackFile();
  });
});
